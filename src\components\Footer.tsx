import { motion } from "framer-motion";
import { ParticleButton } from "@/components/ui/particle-button";
import { <PERSON>U<PERSON>, Github, Linkedin, Send } from "lucide-react";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";

export default function Footer() {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const currentYear = new Date().getFullYear();
  const isRTL = i18n.language === 'ar';
  
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const emailInput = form.elements.namedItem('email') as HTMLInputElement;
    
    if (emailInput.value) {
      toast({
        title: t('footer.newsletter.thanks'),
        description: t('footer.newsletter.confirmation'),
        duration: 5000,
      });
      emailInput.value = '';
    }
  };

  const footerLinks = [
    { href: "#about", label: t('header.about') },
    { href: "/projects", label: t('header.projects') },
    { href: "#blog", label: t('header.blog') },
    { href: "#contact", label: t('header.contact') },
  ];

  const socialLinks = [
    { href: "https://github.com", icon: <Github className="h-5 w-5" />, label: "GitHub" },
    { href: "https://linkedin.com", icon: <Linkedin className="h-5 w-5" />, label: "LinkedIn" },
  ];

  return (
    <footer className="relative bg-gradient-to-b from-black to-[#0a0a0a] py-16 border-t border-white/10 overflow-hidden">
      {/* Background effect */}
      <div className="absolute inset-0 z-0">
        <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-black via-black/60 to-transparent"></div>
        <div className="absolute -top-1/2 -right-1/2 w-full h-full rounded-full bg-neon/5 blur-3xl"></div>
        <div className="absolute -bottom-1/2 -left-1/2 w-full h-full rounded-full bg-neon/5 blur-3xl"></div>
      </div>

      <div className="container relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-10 mb-16">
          {/* Logo and description */}
          <motion.div 
            className="md:col-span-4 flex flex-col space-y-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <a href="#home" className="text-xl sm:text-2xl font-bold">
              {isRTL ? (
                <span className="text-gradient font-rakkas text-3xl">الطيب</span>
              ) : (
                <span className="text-gradient font-limelight">Altayeb</span>
              )}
            </a>
            <p className="text-gray-400 max-w-sm">
              {t('footer.building')}
            </p>
            <div className="flex flex-wrap gap-2 mt-4">
              {socialLinks.map((link, index) => (
                <motion.a 
                  key={index}
                  href={link.href} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-darkgray p-2.5 rounded-lg text-gray-400 hover:text-neon hover:bg-darkgray/80 transition-all duration-300 group"
                  aria-label={link.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 200, 255, 0.15)' }}
                >
                  {link.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick links */}
          <motion.div 
            className="md:col-span-3 flex flex-col space-y-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h3 className="text-white text-lg font-semibold">{t('footer.quickLinks')}</h3>
            <ul className="space-y-3">
              {footerLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-gray-400 hover:text-neon transition-colors duration-300 flex items-center gap-2 group"
                  >
                    <span className="h-0.5 w-0 bg-neon group-hover:w-4 transition-all duration-300"></span>
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Newsletter */}
          <motion.div 
            className="md:col-span-5"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h3 className="text-white text-lg font-semibold mb-4">{t('footer.newsletter.title')}</h3>
            <p className="text-gray-400 mb-4">{t('footer.newsletter.description')}</p>
            
            <form onSubmit={handleSubscribe} className="flex gap-2">
              <div className="flex-1">
                <Input 
                  name="email"
                  type="email" 
                  placeholder={t('footer.newsletter.placeholder')} 
                  required
                  className="bg-darkgray border-darkgray focus-visible:ring-neon text-white h-12"
                />
              </div>
              <ParticleButton 
                type="submit"
                className="bg-neon text-dark font-medium h-12 px-4 hover:bg-neon/90"
                successDuration={1000}
              >
                <span className="flex items-center gap-2">
                  {t('footer.newsletter.button')} <Send className="h-4 w-4" />
                </span>
              </ParticleButton>
            </form>
          </motion.div>
        </div>

        {/* Divider */}
        <motion.div 
          className="h-px w-full bg-white/10 mb-8"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        ></motion.div>

        {/* Bottom section */}
        <div className="flex flex-col md:flex-row justify-between items-center">
          <motion.p 
            className="text-gray-400 text-center md:text-left"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            &copy; {currentYear} Altayeb's Portfolio. {t('footer.rights')}
          </motion.p>
        </div>
      </div>
    </footer>
  );
}
