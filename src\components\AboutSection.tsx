
import { motion } from "framer-motion";
import { 
  Card, 
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Brain, Globe, Workflow, Smartphone, Code } from "lucide-react";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';

const fadeInUp = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1 }
};

export default function AboutSection() {
  const { t } = useTranslation();
  return (
    <section id="about" className="section bg-black/30 py-20">
      <div className="container">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6 }}
        >
          <h2 className={`text-3xl md:text-5xl font-bold mb-4 ${getFontClass()}`}>{t('about.title')}</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            {t('about.subtitle')}
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-dark border border-white/10 h-full transform transition-all duration-500 hover:-translate-y-2 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)]">
              <CardHeader>
                <CardTitle className={`text-2xl text-gradient ${getFontClass()}`}>{t('about.story.title')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-300">
                  {t('about.story.p1')}
                </p>
                <p className="text-gray-300">
                  {t('about.story.p2')}
                </p>
                <p className="text-gray-300">
                  {t('about.story.p3')}
                </p>
                <p className="text-gray-300">
                  {t('about.story.p4')}
                </p>
              </CardContent>
            </Card>
          </motion.div>
          
          <div className="grid grid-cols-1 gap-6">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="bg-dark border border-white/10 transform transition-all duration-500 hover:-translate-y-1 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)]">
                <CardHeader>
                  <CardTitle className={`text-xl text-gradient flex items-center gap-2 ${getFontClass()}`}>
                    <Code className="h-5 w-5" /> {t('about.development.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col gap-2">
                      <div className="p-2 rounded-lg bg-neon/10 text-neon flex items-center gap-2">
                        <Globe className="h-4 w-4" /> {t('about.development.ecommerce')}
                      </div>
                      <span className="text-sm text-gray-300">{t('about.development.ecommerceDesc')}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="p-2 rounded-lg bg-neon/10 text-neon flex items-center gap-2">
                        <Brain className="h-4 w-4" /> {t('about.development.aiIntegration')}
                      </div>
                      <span className="text-sm text-gray-300">{t('about.development.aiIntegrationDesc')}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="p-2 rounded-lg bg-neon/10 text-neon flex items-center gap-2">
                        <Code className="h-4 w-4" /> {t('about.development.webApps')}
                      </div>
                      <span className="text-sm text-gray-300">{t('about.development.webAppsDesc')}</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="p-2 rounded-lg bg-neon/10 text-neon flex items-center gap-2">
                        <Smartphone className="h-4 w-4" /> {t('about.development.mobile')}
                      </div>
                      <span className="text-sm text-gray-300">{t('about.development.mobileDesc')}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="bg-dark border border-white/10 transform transition-all duration-500 hover:-translate-y-1 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)]">
                <CardHeader>
                  <CardTitle className={`text-xl text-gradient ${getFontClass()}`}>{t('about.skills.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-3">
                    <SkillTag>TypeScript</SkillTag>
                    <SkillTag>React</SkillTag>
                    <SkillTag>Node.js</SkillTag>
                    <SkillTag>Next.js</SkillTag>
                    <SkillTag>Claude AI</SkillTag>
                    <SkillTag>MongoDB</SkillTag>
                    <SkillTag>TailwindCSS</SkillTag>
                    <SkillTag>Firebase</SkillTag>
                    <SkillTag>MySQL</SkillTag>
                    <SkillTag>GraphQL</SkillTag>
                    <SkillTag>Python</SkillTag>
                    <SkillTag>AWS</SkillTag>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>

        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="flex justify-center"
        >
          <Button 
            variant="outline" 
            className="border-neon text-neon hover:bg-neon hover:text-dark btn-glow text-lg px-8 py-6 h-auto group"
            asChild
          >
            <a href="/projects">
              {t('about.viewProjects')}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </a>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}

function SkillTag({ children }: { children: React.ReactNode }) {
  return (
    <div className="px-3 py-2 bg-darkgray rounded-full text-sm text-white/80 hover:text-neon hover:bg-darkgray/80 transition-all duration-300 transform hover:scale-105 text-center">
      {children}
    </div>
  );
}
