import React from 'react';
import { tokenizeCode, getTokenClassName, type HighlightedToken } from '@/utils/syntaxHighlighter';

interface SyntaxHighlighterProps {
  code: string;
  language: 'html' | 'css' | 'javascript';
  className?: string;
  showCursor?: boolean;
}

/**
 * Safe syntax highlighter component that doesn't use dangerouslySetInnerHTML
 */
export function SyntaxHighlighter({ 
  code, 
  language, 
  className = '', 
  showCursor = false 
}: SyntaxHighlighterProps) {
  const tokens = tokenizeCode(code, language);
  
  return (
    <pre className={`text-white font-mono text-sm ${className}`}>
      <code className={`language-${language}`}>
        {tokens.map((token, index) => (
          <span 
            key={index} 
            className={getTokenClassName(token.type)}
          >
            {token.content}
          </span>
        ))}
        {showCursor && (
          <span className="text-neon inline-block w-2 animate-blink">|</span>
        )}
      </code>
    </pre>
  );
}

interface TypewriterSyntaxHighlighterProps extends SyntaxHighlighterProps {
  displayText: string;
}

/**
 * Typewriter effect syntax highlighter for the hero section
 */
export function TypewriterSyntaxHighlighter({ 
  code, 
  language, 
  displayText, 
  className = '' 
}: TypewriterSyntaxHighlighterProps) {
  // Only tokenize the displayed portion of the code
  const tokens = tokenizeCode(displayText, language);
  
  return (
    <pre className={`text-white font-mono text-sm ${className}`}>
      <code className={`language-${language}`}>
        {tokens.map((token, index) => (
          <span 
            key={index} 
            className={getTokenClassName(token.type)}
          >
            {token.content}
          </span>
        ))}
        <span className="text-neon inline-block w-2 animate-blink">|</span>
      </code>
    </pre>
  );
}
