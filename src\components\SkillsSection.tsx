
import { motion } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';

const skills = [
  { name: "TypeScript", level: 90, icon: "💻" },
  { name: "React", level: 95, icon: "⚛️" },
  { name: "Node.js", level: 85, icon: "🟢" },
  { name: "Next.js", level: 80, icon: "▲" },
  { name: "Prompt Engineering", level: 95, icon: "🤖" },
  { name: "TailwindCSS", level: 90, icon: "🎨" },
  { name: "MongoDB", level: 75, icon: "🍃" },
  { name: "Firebase", level: 80, icon: "🔥" }
];

export default function SkillsSection() {
  const { t } = useTranslation();
  return (
    <section className="py-12 md:py-20 bg-gradient-to-b from-black/40 to-dark overflow-hidden">
      <div className="container w-full max-w-7xl mx-auto px-4 md:px-8">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0 }
          }}
          transition={{ duration: 0.6 }}
        >
          <h2 className={`text-3xl md:text-5xl font-bold mb-4 ${getFontClass()}`}>{t('skills.title')}</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            {t('skills.subtitle')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 max-w-4xl mx-auto">
          {skills.map((skill, index) => (
            <SkillBar 
              key={skill.name} 
              skill={skill} 
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

function SkillBar({ skill, index }: { skill: { name: string; level: number; icon: string }; index: number }) {
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setProgress(skill.level);
    }, 400 + index * 200);
    
    return () => clearTimeout(timer);
  }, [skill.level, index]);

  return (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <span className="text-xl">{skill.icon}</span>
          <h3 className={`text-lg font-medium ${getFontClass()}`}>{skill.name}</h3>
        </div>
        <span className="text-neon font-mono">{progress}%</span>
      </div>
      <Progress 
        value={progress} 
        className="h-2 bg-darkgray progress-indicator-custom"
        style={{
          "--progress-background": "transparent",
        } as React.CSSProperties}
      />
      {/* Add a style tag in the component to apply the gradient */}
    </motion.div>
  );
}
