import React from "react";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";

interface InteractiveHoverButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text?: string;
  icon?: React.ReactNode;
  variant?: "default" | "outline" | "transparent" | "shine";
  asChild?: boolean;
}

const InteractiveHoverButton = React.forwardRef<
  HTMLButtonElement,
  InteractiveHoverButtonProps
>(({ text, className, icon, variant = "default", asChild = false, children, style, ...props }, ref) => {
  const Comp = asChild ? Slot : "button";
  const buttonText = text || (typeof children === "string" ? children : "Button");
  const buttonIcon = icon || <ArrowRight />;
  
  // Get primary color from style or CSS variable
  const primaryColor = style?.color || 'var(--tw-primary-color, hsl(var(--primary)))';
  const primaryForeground = style?.['--tw-primary-foreground'] || 'hsl(var(--primary-foreground))';
  
  return (
    <Comp
      ref={ref}
      className={cn(
        "group relative w-32 cursor-pointer overflow-hidden rounded-full border p-2 text-center font-semibold transition-all duration-300",
        variant === "outline" && "border-primary text-primary hover:bg-primary/10",
        variant === "transparent" && "border-none bg-transparent hover:bg-transparent",
        variant === "shine" && "border-none bg-transparent hover:bg-transparent",
        className,
      )}
      style={{
        ...style,
        '--interactive-primary': primaryColor,
        '--interactive-foreground': primaryForeground,
      } as React.CSSProperties}
      {...props}
    >
      {asChild ? (
        variant === "shine" ? (
          <div className="relative flex items-center justify-center gap-2 transition-all duration-300 group-hover:scale-105">
            {children}
            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-transparent via-[var(--interactive-primary)] to-transparent opacity-0 blur-xl transition-all duration-500 group-hover:opacity-20"></div>
            <div className="absolute -inset-1 -z-10 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <div className="absolute inset-0 rotate-180 scale-x-[2] scale-y-150">
                <div className="absolute inset-0 animate-wave bg-gradient-to-r from-transparent via-[var(--interactive-primary)] to-transparent opacity-20 blur-xl"></div>
                <div className="absolute inset-0 animate-wave animation-delay-1000 bg-gradient-to-r from-transparent via-[var(--interactive-primary)] to-transparent opacity-20 blur-xl"></div>
              </div>
            </div>
            <div className="absolute inset-0 -z-10 bg-[var(--interactive-primary)] opacity-0 transition-opacity duration-300 group-hover:opacity-10"></div>
          </div>
        ) : (
          children
        )
      ) : (
        <>
          <span className="inline-block translate-x-1 transition-all duration-300 group-hover:translate-x-12 group-hover:opacity-0">
            {buttonText}
          </span>
          <div className="absolute top-0 z-10 flex h-full w-full translate-x-12 items-center justify-center gap-2 opacity-0 transition-all duration-300 group-hover:-translate-x-1 group-hover:opacity-100" style={{ color: 'var(--interactive-foreground)' }}>
            <span>{buttonText}</span>
            {buttonIcon}
          </div>
          {variant !== "shine" && (
            <div className="absolute left-[20%] top-[40%] h-2 w-2 scale-[1] rounded-lg transition-all duration-300 group-hover:left-[0%] group-hover:top-[0%] group-hover:h-full group-hover:w-full group-hover:scale-[1.8]" style={{ backgroundColor: 'var(--interactive-primary)' }}></div>
          )}
        </>
      )}
    </Comp>
  );
});

InteractiveHoverButton.displayName = "InteractiveHoverButton";

export { InteractiveHoverButton };
