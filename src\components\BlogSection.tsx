
import { useState } from 'react';
import { motion } from "framer-motion";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Book<PERSON><PERSON>, ArrowRight, Calendar, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const blogPosts = [
  {
    id: 1,
    title: "The Future of AI in E-commerce",
    excerpt: "Exploring how artificial intelligence is reshaping the online shopping experience with personalized recommendations, chatbots, and automated inventory management.",
    date: "April 15, 2025",
    category: "AI",
    readTime: "5 min read",
    slug: "future-of-ai-ecommerce",
    image: "https://placehold.co/800x600/121212/00FFF7/png?text=AI+in+E-commerce"
  },
  {
    id: 2,
    title: "Building Responsive React Components",
    excerpt: "A deep dive into creating truly responsive and accessible React components that work seamlessly across all devices, with practical examples and best practices.",
    date: "March 28, 2025",
    category: "React",
    readTime: "8 min read",
    slug: "responsive-react-components",
    image: "https://placehold.co/800x600/121212/00FFF7/png?text=React+Components"
  },
  {
    id: 3,
    title: "Prompt Engineering for Large Language Models",
    excerpt: "Learn advanced techniques for crafting effective prompts that yield optimal results from Claude, GPT, and other large language models in various applications.",
    date: "March 10, 2025",
    category: "Prompt Engineering",
    readTime: "7 min read",
    slug: "prompt-engineering-llms",
    image: "https://placehold.co/800x600/121212/00FFF7/png?text=Prompt+Engineering"
  },
];

export default function BlogSection() {
  const fadeInUp = {
    hidden: { y: 40, opacity: 0 },
    show: { y: 0, opacity: 1, transition: { duration: 0.6 } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  return (
    <section id="blog" className="section bg-black/30 py-24">
      <div className="container">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4 font-cairo">Latest Insights</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            Thoughts and articles on AI integration, prompt engineering, and modern web development techniques.
          </p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          {blogPosts.map((post) => (
            <BlogPostCard key={post.id} post={post} variants={fadeInUp} />
          ))}
        </motion.div>

        <motion.div 
          className="flex justify-center"
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <Button 
            className="bg-darkgray hover:bg-neon hover:text-dark group text-lg px-8 py-6 h-auto transition-colors duration-300"
            asChild
          >
            <a href="#blog">
              View All Articles 
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
            </a>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  readTime: string;
  slug: string;
  image: string;
}

function BlogPostCard({ post, variants }: { post: BlogPost, variants: any }) {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div variants={variants}>
      <Card 
        className="bg-dark border border-white/10 overflow-hidden h-full flex flex-col transform transition-all duration-500 hover:-translate-y-2 hover:shadow-[0_0_30px_rgba(0,255,247,0.15)]"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="aspect-[16/9] overflow-hidden">
          <img 
            src={post.image} 
            alt={post.title}
            className={`object-cover w-full h-full transition-all duration-700 ${isHovered ? 'scale-110 brightness-110' : 'scale-100'}`}
          />
        </div>
        <CardHeader>
          <div className="flex justify-between items-center mb-2">
            <Badge variant="outline" className="text-xs border-neon text-neon bg-neon/5 px-3">
              {post.category}
            </Badge>
            <CardDescription className="text-gray-400 text-xs flex items-center gap-1">
              <Clock className="h-3 w-3" /> {post.readTime}
            </CardDescription>
          </div>
          <CardTitle className={`text-xl font-cairo transition-all duration-300 ${isHovered ? 'text-neon' : ''}`}>
            {post.title}
          </CardTitle>
          <CardDescription className="flex items-center gap-1 text-gray-400 text-xs">
            <Calendar className="h-3 w-3" /> {post.date}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1">
          <p className="text-gray-300">{post.excerpt}</p>
        </CardContent>
        <CardFooter>
          <Button 
            variant="ghost" 
            className="text-neon hover:bg-neon/10 group w-full justify-center"
            asChild
          >
            <a href={`#blog/${post.slug}`}>
              Read More 
              <BookOpen className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </a>
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
