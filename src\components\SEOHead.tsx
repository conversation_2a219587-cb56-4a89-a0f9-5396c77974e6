import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
  structuredData?: object;
}

export function SEOHead({
  title,
  description,
  keywords = [],
  image = '/favcon.png',
  url,
  type = 'website',
  article,
  structuredData
}: SEOHeadProps) {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const isRTL = currentLang === 'ar';
  
  // Default values
  const siteUrl = 'https://altyb.netlify.app';
  const defaultTitle = isRTL ? 'الطيب - مطور ويب متخصص في الذكاء الاصطناعي' : 'Altayeb - AI-Enhanced Full Stack Developer';
  const defaultDescription = isRTL 
    ? 'مطور ويب متخصص في الذكاء الاصطناعي وهندسة الأوامر. أقوم ببناء تطبيقات ويب حديثة ومتاجر إلكترونية متقدمة باستخدام React وTypeScript.'
    : 'AI-Enhanced Full Stack Developer specializing in prompt engineering and modern web applications. Building cutting-edge e-commerce platforms and web solutions with React, TypeScript, and AI integration.';
  
  const pageTitle = title ? `${title} | ${defaultTitle}` : defaultTitle;
  const pageDescription = description || defaultDescription;
  const pageUrl = url ? `${siteUrl}${url}` : siteUrl;
  const pageImage = image.startsWith('http') ? image : `${siteUrl}${image}`;
  
  // Default keywords
  const defaultKeywords = isRTL 
    ? ['مطور ويب', 'الذكاء الاصطناعي', 'هندسة الأوامر', 'React', 'TypeScript', 'متاجر إلكترونية', 'تطوير مواقع', 'الطيب']
    : ['web developer', 'AI developer', 'prompt engineering', 'React', 'TypeScript', 'e-commerce', 'full stack', 'Altayeb'];
  
  const allKeywords = [...defaultKeywords, ...keywords];

  // Default structured data for person/developer
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "Altayeb",
    "jobTitle": isRTL ? "مطور ويب متخصص في الذكاء الاصطناعي" : "AI-Enhanced Full Stack Developer",
    "description": pageDescription,
    "url": siteUrl,
    "image": pageImage,
    "sameAs": [
      "https://github.com/altayeb-ai",
      "https://linkedin.com/in/altayeb-ai"
    ],
    "knowsAbout": [
      "Web Development",
      "Artificial Intelligence",
      "Prompt Engineering",
      "React",
      "TypeScript",
      "E-commerce Development",
      "Full Stack Development"
    ],
    "worksFor": {
      "@type": "Organization",
      "name": "Freelance Developer"
    }
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={allKeywords.join(', ')} />
      <meta name="author" content="Altayeb" />
      <meta name="robots" content="index, follow" />
      <meta name="language" content={currentLang} />
      <meta name="revisit-after" content="7 days" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={pageUrl} />
      
      {/* Open Graph Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:image" content={pageImage} />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="Altayeb Portfolio" />
      <meta property="og:locale" content={isRTL ? 'ar_SA' : 'en_US'} />
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={pageImage} />
      <meta name="twitter:creator" content="@altayeb_ai" />
      
      {/* Article specific meta tags */}
      {type === 'article' && article && (
        <>
          <meta property="article:published_time" content={article.publishedTime} />
          <meta property="article:modified_time" content={article.modifiedTime} />
          <meta property="article:author" content={article.author || 'Altayeb'} />
          <meta property="article:section" content={article.section} />
          {article.tags?.map(tag => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#00FFF7" />
      <meta name="msapplication-TileColor" content="#00FFF7" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Language alternates */}
      <link rel="alternate" hrefLang="en" href={pageUrl.replace('/ar/', '/en/')} />
      <link rel="alternate" hrefLang="ar" href={pageUrl.replace('/en/', '/ar/')} />
      <link rel="alternate" hrefLang="x-default" href={pageUrl} />
    </Helmet>
  );
}
