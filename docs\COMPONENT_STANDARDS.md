# Component Standards and Guidelines

## File Naming Conventions

- **Components**: PascalCase (e.g., `UserProfile.tsx`, `NavigationMenu.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`, `useLocalStorage.ts`)
- **Utils**: camelCase (e.g., `formatDate.ts`, `apiHelpers.ts`)
- **Types**: PascalCase with `.types.ts` suffix (e.g., `User.types.ts`)

## Component Structure

```typescript
/**
 * Component description
 * @param prop1 - Description of prop1
 * @param prop2 - Description of prop2
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

// Types
interface ComponentProps {
  prop1: string;
  prop2?: number;
  children?: React.ReactNode;
  className?: string;
}

// Component
export function ComponentName({ 
  prop1, 
  prop2 = 0, 
  children, 
  className = '' 
}: ComponentProps) {
  const { t } = useTranslation();
  
  // State
  const [state, setState] = useState<StateType>(initialState);
  
  // Memoized values
  const memoizedValue = useMemo(() => {
    return expensiveCalculation(prop1);
  }, [prop1]);
  
  // Callbacks
  const handleAction = useCallback(() => {
    // Handle action
  }, []);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, []);
  
  return (
    <motion.div 
      className={`component-base-styles ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {children}
    </motion.div>
  );
}

export default ComponentName;
```

## Export Patterns

### Preferred: Named Exports
```typescript
export function ComponentName() { ... }
export { ComponentName };
```

### When to use Default Exports
- Page components
- Main application components
- Single-purpose utility functions

## TypeScript Standards

### Interface Naming
```typescript
// Component props
interface ComponentNameProps {
  // props
}

// Data models
interface User {
  id: string;
  name: string;
}

// API responses
interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}
```

### Generic Types
```typescript
interface GenericComponent<T> {
  data: T;
  onSelect: (item: T) => void;
}
```

## Performance Guidelines

### Memoization
- Use `useMemo` for expensive calculations
- Use `useCallback` for event handlers passed to child components
- Use `React.memo` for components that receive stable props

### Code Splitting
```typescript
const LazyComponent = React.lazy(() => import('./LazyComponent'));
```

## Accessibility Standards

### Required Attributes
- `aria-label` for interactive elements without text
- `role` for custom interactive elements
- `tabIndex` for keyboard navigation
- `aria-expanded` for collapsible elements

### Focus Management
```typescript
const focusRef = useRef<HTMLElement>(null);

useEffect(() => {
  if (isOpen && focusRef.current) {
    focusRef.current.focus();
  }
}, [isOpen]);
```

## Animation Guidelines

### Framer Motion Patterns
```typescript
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const stagger = {
  visible: {
    transition: {
      staggerChildren: 0.1
    }
  }
};
```

## Error Handling

### Component Error Boundaries
```typescript
import { withErrorBoundary } from '@/components/ErrorBoundary';

export default withErrorBoundary(ComponentName);
```

### Async Error Handling
```typescript
import { safeAsync } from '@/utils/errorHandling';

const handleAsyncAction = async () => {
  const { data, error } = await safeAsync(() => apiCall());
  
  if (error) {
    // Handle error
    return;
  }
  
  // Use data
};
```

## Testing Guidelines

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { ComponentName } from './ComponentName';

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName prop1="test" />);
    expect(screen.getByText('test')).toBeInTheDocument();
  });
  
  it('handles user interaction', () => {
    const handleClick = jest.fn();
    render(<ComponentName onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
});
```

## Documentation Standards

### Component Documentation
```typescript
/**
 * A reusable button component with multiple variants and sizes.
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="lg" onClick={handleClick}>
 *   Click me
 * </Button>
 * ```
 */
```

### Hook Documentation
```typescript
/**
 * Custom hook for managing local storage state
 * 
 * @param key - The localStorage key
 * @param initialValue - Initial value if key doesn't exist
 * @returns [value, setValue] tuple
 * 
 * @example
 * ```tsx
 * const [user, setUser] = useLocalStorage('user', null);
 * ```
 */
```

## Code Review Checklist

- [ ] Component follows naming conventions
- [ ] TypeScript interfaces are properly defined
- [ ] Accessibility attributes are included
- [ ] Performance optimizations are applied where needed
- [ ] Error handling is implemented
- [ ] Component is properly documented
- [ ] Tests are written and passing
- [ ] No console.log statements in production code
- [ ] Imports are organized and unused imports removed
