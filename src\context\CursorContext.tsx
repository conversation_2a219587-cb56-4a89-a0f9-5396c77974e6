import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface CursorPosition {
  x: number;
  y: number;
}

interface CursorContextType {
  cursorPosition: CursorPosition;
  setCursorPosition: React.Dispatch<React.SetStateAction<CursorPosition>>;
}

const CursorContext = createContext<CursorContextType | undefined>(undefined);

export const useCursor = (): CursorContextType => {
  const context = useContext(CursorContext);
  if (!context) {
    throw new Error("useCursor must be used within a CursorProvider");
  }
  return context;
};

interface CursorProviderProps {
  children: ReactNode;
}

export const CursorProvider: React.FC<CursorProviderProps> = ({ children }) => {
  const [cursorPosition, setCursorPosition] = useState<CursorPosition>({ x: 0.5, y: 0.5 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      // Get the cursor position relative to the window
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;
      setCursorPosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <CursorContext.Provider value={{ cursorPosition, setCursorPosition }}>
      {children}
    </CursorContext.Provider>
  );
}; 