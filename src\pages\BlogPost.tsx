import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from "framer-motion";
import { ArrowLeft, Calendar, Clock, Tag } from "lucide-react"; // Removed Copy, Check as they are handled by CSS/inline SVG now
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
// Make sure to import i18n object along with t
import { useTranslation } from "react-i18next";
// Tooltip related imports seem unused in the provided logic, but kept if needed elsewhere
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// --- DEVELOPER COMMENT ---
// Central data source for blog posts with multilingual support.
// Fields like title, excerpt, and content are now objects with 'en' and 'ar' keys.
// Ensure HTML content in 'ar' is also properly structured and escaped if needed.
const blogPosts = [
    {
        id: 3,
        title: {
            en: "Prompt Engineering for Large Language Models",
            ar: "هندسة الأوامر لنماذج اللغة الكبيرة"
        },
        excerpt: {
            en: "Learn advanced techniques for crafting effective prompts that yield optimal results from Claude, GPT, and other large language models in various applications.",
            ar: "تعلم تقنيات متقدمة لصياغة أوامر فعالة تحقق أفضل النتائج من Claude و GPT ونماذج اللغة الكبيرة الأخرى في تطبيقات متنوعة."
        },
        date: "March 10, 2025",
        category: "Prompt Engineering", // Consider translating category if needed elsewhere
        readTime: "7 min read", // Consider dynamic read time based on language/content length
        slug: "prompt-engineering-llms", // Slugs usually remain language-independent
        featured: true,
        trending: false,
        tags: ["AI", "prompt engineering", "LLMs", "natural language processing", "GPT", "Claude"], // Tags could also be translated if required
        content: {
            en: `
            <h2>The Art and Science of Prompt Engineering</h2>
            <p>As large language models (LLMs) like GPT-4, Claude, and others become increasingly powerful, the ability to effectively communicate with these models through well-crafted prompts has emerged as a crucial skill.</p>
            <p>Prompt engineering is the practice of designing inputs that guide AI models to produce desired outputs. It's a blend of psychology, linguistics, and computer science that can dramatically improve the quality and usefulness of AI-generated content.</p>
            <h2>Core Principles of Effective Prompts</h2>
            <h3>1. Be Specific and Clear</h3>
            <p>Vague prompts lead to vague responses. Specify exactly what you want, including the format, tone, length, and perspective.</p>
            <p><strong>Instead of:</strong> "Tell me about climate change."</p>
            <p><strong>Try:</strong> "Provide a 300-word summary of the latest IPCC report findings on climate change, focusing on projected impacts for coastal cities. Include 3 key statistics and use an informative, scientific tone."</p>
            <h3>2. Provide Context and Constraints</h3>
            <p>LLMs perform better when given relevant background information and clear constraints:</p>
            <p>"You are helping a team of software engineers implement a new feature. They use TypeScript and React. The feature needs to allow users to filter a list of products by multiple criteria simultaneously. Suggest an approach that emphasizes code reusability and performance, with example code snippets."</p>
            <h3>3. Use Role Prompting</h3>
            <p>Assigning a role to the AI can help frame its responses appropriately:</p>
            <p>"Act as an experienced data scientist reviewing a machine learning model. The model is a random forest classifier used for credit risk assessment. Identify potential issues with this approach and suggest improvements, focusing on explainability and bias mitigation."</p>
            <h3>4. Chain of Thought Prompting</h3>
            <p>For complex reasoning tasks, guide the model to think step by step:</p>
            <p>"Let's solve this probability problem step by step. In a group of 30 people, what's the probability that at least two people share the same birthday? Start by identifying the approach, then calculate the probability systematically."</p>
            <h2>Advanced Techniques</h2>
            <h3>1. Few-Shot Learning</h3>
            <p>Provide examples of the input-output pairs you want:</p>
            <pre><code>
Input: "The movie was absolutely terrible."
Sentiment: Negative

Input: "I enjoyed parts of the film, but the ending was disappointing."
Sentiment: Mixed

Input: "The new restaurant offers amazing food at reasonable prices."
Sentiment:
            </code></pre>
            <h3>2. Iterative Refinement</h3>
            <p>Use the output of one prompt as input to another, gradually refining the result:</p>
            <p>First prompt: "Generate a basic outline for an article about quantum computing for beginners."</p>
            <p>Second prompt: "Take this outline and expand section 3 with more details about quantum entanglement, using analogies that would help non-technical readers understand."</p>
            <h3>3. Negative Prompting</h3>
            <p>Specify what you don't want:</p>
            <p>"Write a creative short story about time travel. Avoid clichés like meeting historical figures or changing major historical events. Don't use paradoxes as plot devices."</p>
            <h2>Ethical Considerations</h2>
            <p>As you develop your prompt engineering skills, remember to:</p>
            <ul>
                <li>Respect content policies and avoid prompts designed to circumvent safety measures</li>
                <li>Be aware of potential biases in model responses</li>
                <li>Consider the environmental impact of extensive model usage</li>
                <li>Properly attribute AI-generated content when used professionally</li>
            </ul>
            <p>By mastering these prompt engineering techniques, you can unlock the full potential of large language models and use them as powerful tools for creativity, problem-solving, and knowledge work.</p>
            `,
            ar: `
            <div dir="rtl">
                <h2>فن وعلم هندسة الأوامر</h2>
                <p>مع تزايد قوة نماذج اللغة الكبيرة (LLMs) مثل GPT-4 و Claude وغيرها، أصبحت القدرة على التواصل الفعال مع هذه النماذج من خلال أوامر مصاغة جيدًا مهارة حاسمة.</p>
                <p>هندسة الأوامر هي ممارسة تصميم المدخلات التي توجه نماذج الذكاء الاصطناعي لإنتاج المخرجات المطلوبة. إنها مزيج من علم النفس واللغويات وعلوم الكمبيوتر يمكن أن يحسن بشكل كبير جودة وفائدة المحتوى الذي يولده الذكاء الاصطناعي.</p>
                <h2>المبادئ الأساسية للأوامر الفعالة</h2>
                <h3>1. كن محددًا وواضحًا</h3>
                <p>الأوامر الغامضة تؤدي إلى استجابات غامضة. حدد بالضبط ما تريد، بما في ذلك التنسيق والنبرة والطول والمنظور.</p>
                <p><strong>بدلاً من:</strong> "أخبرني عن تغير المناخ."</p>
                <p><strong>جرب:</strong> "قدم ملخصًا من 300 كلمة لنتائج تقرير الهيئة الحكومية الدولية المعنية بتغير المناخ الأخير حول تغير المناخ، مع التركيز على التأثيرات المتوقعة للمدن الساحلية. قم بتضمين 3 إحصائيات رئيسية واستخدم نبرة علمية وغنية بالمعلومات."</p>

                <p>[المحتوى باللغة العربية لهندسة الأوامر يوضع هنا...]</p>

                <h2>الاعتبارات الأخلاقية</h2>
                <p>بينما تطور مهاراتك في هندسة الأوامر، تذكر أن:</p>
                <ul>
                    <li>تحترم سياسات المحتوى وتتجنب الأوامر المصممة للتحايل على تدابير السلامة</li>
                    <li>تكون على دراية بالتحيزات المحتملة في استجابات النموذج</li>
                    <li>تأخذ في الاعتبار التأثير البيئي للاستخدام المكثف للنماذج</li>
                    <li>تنسب المحتوى الذي تم إنشاؤه بواسطة الذكاء الاصطناعي بشكل صحيح عند استخدامه مهنيًا</li>
                </ul>
                <p>من خلال إتقان تقنيات هندسة الأوامر هذه، يمكنك إطلاق العنان للإمكانات الكاملة لنماذج اللغة الكبيرة واستخدامها كأدوات قوية للإبداع وحل المشكلات والعمل المعرفي.</p>
            </div>
            `
        }
    },
    {
        id: 4,
        title: {
            en: "Web Development Fundamentals: HTML & CSS",
            ar: "أساسيات تطوير الويب: HTML و CSS"
        },
        excerpt: {
            en: "A beginner's guide to the building blocks of the web. Learn how HTML structures content and CSS styles it, with a simple card component example.",
            ar: "دليل المبتدئين لبنات بناء الويب. تعلم كيف يقوم HTML ببناء المحتوى وكيف يقوم CSS بتنسيقه، مع مثال لمكون بطاقة بسيط."
        },
        date: "April 27, 2025",
        category: "Coding",
        readTime: "6 min read",
        slug: "web-dev-fundamentals-html-css",
        featured: true,
        trending: true,
        tags: ["coding", "web development", "html", "css", "frontend", "beginner"],
        content: {
            en: `
            <h2>Welcome to Web Development!</h2>
            <p>Every website you visit is built using a combination of technologies. At the core are HTML and CSS, the fundamental building blocks for structuring and styling web pages.</p>
            <h3>HTML: The Structure</h3>
            <p>HyperText Markup Language (HTML) provides the skeleton of a webpage. It uses tags (like <code>&lt;p&gt;</code> for paragraph, <code>&lt;h1&gt;</code> for heading, <code>&lt;div&gt;</code> for division/container) to define different types of content and how they are organized.</p>
            <p>Think of it like the frame of a house – it defines the rooms and walls, but not the paint color or furniture.</p>
            <h3>CSS: The Style</h3>
            <p>Cascading Style Sheets (CSS) is used to control the presentation, layout, and appearance of the HTML content. You can define colors, fonts, spacing, positioning, and even animations.</p>
            <p>CSS brings the visual design to the structure defined by HTML, like choosing paint colors, arranging furniture, and adding decorations to the house.</p>
            <h2>Example: A Simple Card Component</h2>
            <p>Let's see how HTML and CSS work together to create a common UI element: a card.</p>
            <h3>HTML Structure (card.html)</h3>
            <p>Here's the basic HTML structure for our card. We use a <code>&lt;div&gt;</code> with a class name (<code>card</code>) to group the elements.</p>
            <pre><code>
&lt;!-- card.html --&gt;
&lt;div class="card"&gt;
  &lt;img src="image-placeholder.jpg" alt="Card Image" class="card-image"&gt;
  &lt;div class="card-content"&gt;
    &lt;h3 class="card-title"&gt;Card Title&lt;/h3&gt;
    &lt;p class="card-text"&gt;This is some descriptive text for the card. It gives a brief overview of the content.&lt;/p&gt;
    &lt;a href="#" class="card-button"&gt;Learn More&lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;
            </code></pre>
            <p><strong>Note:</strong> We use <code>&amp;lt;</code> and <code>&amp;gt;</code> to display the HTML tags as text within the code block.</p>
            <h3>CSS Styling (style.css)</h3>
            <p>Now, let's add some CSS to make it look like a card.</p>
            <pre><code>
/* style.css */
.card {
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden; /* Keeps image corners rounded */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  font-family: sans-serif;
  background-color: #fff; /* Assuming a light theme */
  color: #333; /* Dark text for contrast */
  margin: 1rem; /* Add some space around the card */
}

.card-image {
  width: 100%;
  height: 180px; /* Fixed height for consistency */
  object-fit: cover; /* Scales image nicely */
  display: block; /* Removes extra space below image */
}

.card-content {
  padding: 1rem;
}

.card-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.card-text {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #666;
}

.card-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #007bff; /* Example blue button */
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.card-button:hover {
  background-color: #0056b3; /* Darker blue on hover */
}
            </code></pre>
            <p>By combining this HTML and CSS (and linking the CSS file to the HTML document), you create a styled card component. This is a fundamental pattern in web development, forming the basis for more complex layouts and interfaces.</p>
            `,
            ar: `
            <div dir="rtl">
                <h2>مرحبًا بك في تطوير الويب!</h2>
                <p>كل موقع تزوره مبني باستخدام مزيج من التقنيات. في الصميم توجد HTML و CSS، وهي اللبنات الأساسية لهيكلة وتنسيق صفحات الويب.</p>
                <h3>HTML: الهيكل</h3>
                <p>توفر لغة ترميز النص التشعبي (HTML) الهيكل العظمي لصفحة الويب. تستخدم علامات (مثل <code>&lt;p&gt;</code> للفقرة، <code>&lt;h1&gt;</code> للعنوان، <code>&lt;div&gt;</code> للتقسيم/الحاوية) لتحديد أنواع مختلفة من المحتوى وكيفية تنظيمها.</p>
                <p>فكر فيها مثل إطار المنزل - فهي تحدد الغرف والجدران، ولكن ليس لون الطلاء أو الأثاث.</p>
                <h3>CSS: النمط</h3>
                <p>تُستخدم أوراق الأنماط المتتالية (CSS) للتحكم في العرض والتخطيط ومظهر محتوى HTML. يمكنك تحديد الألوان والخطوط والتباعد والموضع وحتى الرسوم المتحركة.</p>
                <p>تجلب CSS التصميم المرئي إلى الهيكل المحدد بواسطة HTML، مثل اختيار ألوان الطلاء وترتيب الأثاث وإضافة الديكورات إلى المنزل.</p>
                <h2>مثال: مكون بطاقة بسيط</h2>
                <p>دعنا نرى كيف تعمل HTML و CSS معًا لإنشاء عنصر واجهة مستخدم شائع: البطاقة.</p>

                <p>[المحتوى باللغة العربية لأساسيات الويب يوضع هنا...]</p>

                <p>من خلال الجمع بين HTML و CSS (وربط ملف CSS بمستند HTML)، يمكنك إنشاء مكون بطاقة منسق. هذا نمط أساسي في تطوير الويب، يشكل الأساس لتخطيطات وواجهات أكثر تعقيدًا.</p>
            </div>
            `
        }
    },
    {
        id: 5,
        title: {
            en: "Crafting Effective Prompts: Beyond the Basics",
            ar: "صياغة أوامر فعالة: ما وراء الأساسيات"
        },
        excerpt: {
            en: "Dive deeper into prompt engineering with structured formats, persona adoption, and techniques for specific tasks like code generation and creative writing.",
            ar: "تعمق أكثر في هندسة الأوامر باستخدام التنسيقات المنظمة، وتبني الشخصيات، وتقنيات للمهام المحددة مثل توليد الأكواد والكتابة الإبداعية."
        },
        date: "April 26, 2025",
        category: "Prompt Engineering",
        readTime: "8 min read",
        slug: "advanced-prompt-engineering-techniques",
        featured: false,
        trending: true,
        tags: ["AI", "prompt engineering", "LLMs", "GPT", "Claude", "structured prompting", "code generation"],
        content: {
            en: `
            <h2>Leveling Up Your Prompting Skills</h2>
            <p>While basic prompt engineering principles like clarity and context are essential, mastering advanced techniques can unlock significantly better results from Large Language Models (LLMs).</p>
            <p>Let's explore structured prompting, persona adoption, and task-specific strategies.</p>
            <h3>Structured Prompt Formats</h3>
            <p>Instead of freeform text, providing a structured template can guide the LLM more precisely. Use clear headings or delimiters.</p>
            <pre><code>
### Task Description
Summarize the key benefits of using TypeScript over JavaScript for large-scale applications.

### Context
Target audience: Junior developers familiar with JavaScript but new to TypeScript.
Key areas to cover: Static typing, code maintainability, tooling, scalability.
Desired output format: A bulleted list (max 5 points), followed by a brief concluding paragraph.
Tone: Informative and encouraging.

### Constraints
- Avoid overly technical jargon where possible.
- Keep the total length under 250 words.
- Do not include code examples in this summary.

### Input (if applicable)
N/A

### Output Start:
Here are the key benefits...
            </code></pre>
            <p>This structure leaves less room for ambiguity and helps the LLM understand all constraints and requirements clearly.</p>
            <h3>Persona Adoption for Nuanced Responses</h3>
            <p>Going beyond simple role-playing ("Act as a pirate"), adopt specific personas with defined characteristics and knowledge bases.</p>
            <p><strong>Example:</strong></p>
            <p>"Adopt the persona of a skeptical, detail-oriented QA engineer reviewing UI mockups for a new mobile banking app. Analyze the provided login screen mockup [Imagine Mockup Description Here] focusing on potential usability issues, edge cases, security concerns, and accessibility gaps. Present your findings as a prioritized list of concerns."</p>
            <p>This yields more targeted and critical feedback than a generic request.</p>
            <h3>Task-Specific Prompting: Code Generation</h3>
            <p>When asking for code, be extremely specific about the language, framework, libraries, desired functionality, and any existing code patterns or constraints.</p>
            <pre><code>
# Task: Generate a React functional component using TypeScript

# Component Name: UserProfileCard

# Props:
# - user: object with properties { id: number, name: string, email: string, avatarUrl?: string }

# Functionality:
# - Display the user's name (as an h3) and email (as a paragraph).
# - If avatarUrl is provided, display an image (img tag) with alt text "User Avatar".
# - If avatarUrl is NOT provided, display a placeholder div with initials (e.g., "JD" for "John Doe").
# - Wrap the entire component in a div with className "profile-card".

# Styling:
# - Use Tailwind CSS classes for basic styling (e.g., padding, border, rounded corners).
# - Center the content within the card.

# Constraints:
# - Use React Hooks (useState, useEffect) if necessary (though likely not for this simple example).
# - Ensure proper TypeScript types for props.

# Generate the component code:
            </code></pre>
            <h3>Task-Specific Prompting: Creative Writing</h3>
            <p>For creative tasks, focus on elements like genre, tone, style, character voice, setting, and specific plot points or constraints.</p>
            <p>"Write a 500-word flash fiction story in the style of magical realism. The story should center around a librarian who discovers that overdue books whisper secrets about their former readers. The tone should be melancholic yet wondrous. Include a brief interaction with one specific whispering book. Avoid a predictable happy ending."</p>
            <p>By employing these more sophisticated prompting techniques, you can guide LLMs to produce outputs that are not only accurate but also tailored precisely to your complex needs and specific contexts.</p>
            `,
            ar: `
            <div dir="rtl">
                <h2>الارتقاء بمهاراتك في صياغة الأوامر</h2>
                <p>في حين أن مبادئ هندسة الأوامر الأساسية مثل الوضوح والسياق ضرورية، فإن إتقان التقنيات المتقدمة يمكن أن يطلق العنان لنتائج أفضل بكثير من نماذج اللغة الكبيرة (LLMs).</p>
                <p>دعنا نستكشف الأوامر المنظمة، وتبني الشخصيات، والاستراتيجيات الخاصة بالمهام.</p>
                <h3>تنسيقات الأوامر المنظمة</h3>
                <p>بدلاً من النص الحر، يمكن أن يؤدي توفير قالب منظم إلى توجيه LLM بشكل أكثر دقة. استخدم عناوين أو محددات واضحة.</p>

                <p>[المحتوى باللغة العربية للأوامر المتقدمة يوضع هنا...]</p>

                <p>من خلال استخدام تقنيات الأوامر الأكثر تطوراً هذه، يمكنك توجيه LLMs لإنتاج مخرجات ليست دقيقة فحسب، بل مصممة أيضًا بدقة لاحتياجاتك المعقدة وسياقاتك المحددة.</p>
            </div>
            `
        }
    },
    {
        id: 6,
        title: {
            en: "What is 'Vibe Coding'? Exploring Intuition and Flow",
            ar: "ما هو 'البرمجة بالحدس'؟ استكشاف البداهة والتدفق"
        },
        excerpt: {
            en: "Beyond syntax and algorithms, 'Vibe Coding' emphasizes the feel, flow, and collaborative energy in software development. Let's explore this mindset.",
            ar: "بعيدًا عن بناء الجملة والخوارزميات، تؤكد 'البرمجة بالحدس' على الإحساس والتدفق والطاقة التعاونية في تطوير البرمجيات. دعنا نستكشف هذه العقلية."
        },
        date: "April 25, 2025",
        category: "Development Culture",
        readTime: "5 min read",
        slug: "what-is-vibe-coding",
        featured: false,
        trending: false,
        tags: ["vibe coding", "developer mindset", "coding style", "collaboration", "flow state", "software development"],
        content: {
            en: `
            <h2>Beyond the Logic: The 'Vibe' in Coding</h2>
            <p>We often talk about coding in terms of logic, algorithms, and syntax. But there's another, more intangible aspect: the 'vibe'. "Vibe Coding" isn't a specific methodology but rather a focus on the experiential and intuitive aspects of software development.</p>
            <h3>What Contributes to the Vibe?</h3>
            <ul>
                <li><strong>Flow State:</strong> That feeling of being fully immersed and energized while coding, where hours can pass like minutes. Environment, focus, and task clarity contribute to this.</li>
                <li><strong>Code Aesthetics & Readability:</strong> Writing code that not only works but *feels* clean, elegant, and easy to understand. Consistent formatting, meaningful variable names, and well-structured functions play a big role.</li>
                <li><strong>Intuition & Experience:</strong> Leveraging past experiences and pattern recognition to make design decisions that feel 'right', even before all the logical steps are fully articulated.</li>
                <li><strong>Collaborative Energy:</strong> The synergy within a team. Positive communication, shared understanding, psychological safety, and constructive feedback loops create a good development vibe.</li>
                <li><strong>Tooling & Environment:</strong> A comfortable and efficient development setup – from the IDE theme and font to build tools and automated processes – can significantly impact the coding experience.</li>
            </ul>
            <h2>Is 'Vibe' Just Fluff?</h2>
            <p>Not necessarily. While subjective, a positive coding vibe often correlates with higher productivity, better code quality, and improved developer well-being. When developers are in flow and enjoy the process, they tend to produce more thoughtful and maintainable code.</p>
            <p>A team with a good collaborative vibe can tackle complex problems more effectively than a technically brilliant but disjointed group.</p>
            <h2>Cultivating the Vibe</h2>
            <p>Individuals can focus on creating a conducive environment, practicing clean code habits, and reflecting on their intuition. Teams can prioritize clear communication, mutual respect, and efficient workflows.</p>
            <p>Sometimes, it's about choosing a slightly more expressive or readable way to write a function, even if it's a few characters longer. Consider this simple example:</p>
            <pre><code>
// Less 'vibey' - functional, but a bit dense
const getUserInitials = (user) => user.firstName && user.lastName ? \`\${user.firstName[0]}\${user.lastName[0]}\`.toUpperCase() : '?';

// Arguably more 'vibey' - clearer steps, easier to read
function getUserInitials(user) {
  if (user && user.firstName && user.lastName) {
    const firstInitial = user.firstName[0];
    const lastInitial = user.lastName[0];
    return \`\${firstInitial}\${lastInitial}\`.toUpperCase();
  }
  // Handle cases where name is missing
  return '?';
}
            </code></pre>
            <p>The second version, while longer, breaks down the logic, making it arguably easier to grasp the intent instantly – contributing to a better 'reading vibe'.</p>
            <p>Ultimately, 'Vibe Coding' is about acknowledging that software development is a human activity, influenced by emotion, intuition, and collaboration, not just cold, hard logic.</p>
            `,
            ar: `
            <div dir="rtl">
                <h2>ما وراء المنطق: 'الحدس' في البرمجة</h2>
                <p>غالبًا ما نتحدث عن البرمجة من حيث المنطق والخوارزميات وبناء الجملة. ولكن هناك جانب آخر غير ملموس: 'الحدس'. "البرمجة بالحدس" ليست منهجية محددة بل هي تركيز على الجوانب التجريبية والبديهية لتطوير البرمجيات.</p>
                <h3>ما الذي يساهم في 'الحدس'؟</h3>
                <ul>
                    <li><strong>حالة التدفق:</strong> ذلك الشعور بالانغماس التام والنشاط أثناء البرمجة، حيث يمكن أن تمر الساعات كالدقائق. تساهم البيئة والتركيز ووضوح المهمة في ذلك.</li>
                    <li><strong>جماليات الكود وقابليته للقراءة:</strong> كتابة كود لا يعمل فقط بل *يبدو* نظيفًا وأنيقًا وسهل الفهم. يلعب التنسيق المتسق وأسماء المتغيرات ذات المغزى والوظائف جيدة التنظيم دورًا كبيرًا.</li>
                    <li><strong>البداهة والخبرة:</strong> الاستفادة من الخبرات السابقة والتعرف على الأنماط لاتخاذ قرارات تصميم تبدو 'صحيحة'، حتى قبل توضيح جميع الخطوات المنطقية بالكامل.</li>
                    <li><strong>الطاقة التعاونية:</strong> التآزر داخل الفريق. يخلق التواصل الإيجابي والتفاهم المشترك والسلامة النفسية وحلقات التغذية الراجعة البناءة أجواء تطوير جيدة.</li>
                    <li><strong>الأدوات والبيئة:</strong> يمكن أن يؤثر إعداد تطوير مريح وفعال - من سمة IDE والخط إلى أدوات البناء والعمليات الآلية - بشكل كبير على تجربة البرمجة.</li>
                </ul>
                <h2>هل 'الحدس' مجرد كلام؟</h2>
                <p>ليس بالضرورة. على الرغم من كونه ذاتيًا، غالبًا ما يرتبط 'الحدس' الإيجابي في البرمجة بإنتاجية أعلى وجودة كود أفضل وتحسين رفاهية المطورين. عندما يكون المطورون في حالة تدفق ويستمتعون بالعملية، فإنهم يميلون إلى إنتاج كود أكثر تفكيرًا وقابلية للصيانة.</p>

                <p>[المحتوى باللغة العربية عن البرمجة بالحدس يوضع هنا...]</p>

                <p>في النهاية، 'البرمجة بالحدس' تدور حول الاعتراف بأن تطوير البرمجيات نشاط بشري، يتأثر بالعاطفة والحدس والتعاون، وليس فقط المنطق البارد والجاف.</p>
            </div>
            `
        }
    }
];


// Type definition for a single blog post (adjust based on your actual data needs)
interface BlogPostData {
    id: number;
    title: { [key: string]: string }; // e.g., { en: "Title", ar: "عنوان" }
    excerpt: { [key: string]: string };
    date: string;
    category: string;
    readTime: string;
    slug: string;
    featured: boolean;
    trending: boolean;
    tags: string[];
    content: { [key: string]: string };
}


// Function to process the raw HTML content string from the blog post data.
// It specifically targets <pre><code> blocks to enhance them.
// This function remains unchanged as it operates on the HTML structure itself.
const processContent = (postContent: string | undefined) => {
    if (!postContent) return postContent;

    // Regex to find <pre><code> blocks and wrap them for copy functionality
    return postContent.replace(
        /<pre><code>([\s\S]*?)<\/code><\/pre>/g,
        (match, codeContent) => {
            // Ensure HTML entities within the code are preserved correctly during replacement
            const escapedCode = codeContent.replace(/"/g, '&quot;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
            return `<div class="relative group code-block">` +
                   `<pre><code>${codeContent}</code></pre>` + // Use original content inside <code>
                   `<button class="copy-code-btn" data-code="${escapedCode}">` + // Use escaped for attribute
                   `<span class="sr-only">Copy code</span></button></div>`;
        }
    );
};


export default function BlogPost() {
    const { slug } = useParams<{ slug: string }>();
    // Get the i18n instance to access the current language
    const { t, i18n } = useTranslation();
    const [post, setPost] = useState<BlogPostData | null>(null);
    const [loading, setLoading] = useState(true);
    const [processedContent, setProcessedContent] = useState<string>("");

    // Get current language code (e.g., 'en', 'ar') or fallback to 'en'
    const currentLang = i18n.language.split('-')[0] || 'en'; // Use base language ('en' instead of 'en-US')

    useEffect(() => {
        // Find the post that matches the slug from the blogPosts array
        const foundPost = blogPosts.find(p => p.slug === slug);
        // Cast foundPost to BlogPostData or null
        setPost(foundPost as BlogPostData | null);
        setLoading(false);
    }, [slug]);

    // Process content when post changes OR when language changes
    useEffect(() => {
        if (post?.content) {
            // Get the content for the current language, fallback to English if not found
            const contentForLang = post.content[currentLang] || post.content.en;
            setProcessedContent(processContent(contentForLang));
        } else {
            setProcessedContent(""); // Clear content if no post
        }
        // Add currentLang to dependency array to re-process content on language change
    }, [post, currentLang]);

    // Effect for adding copy button interactivity and syntax highlighting
    useEffect(() => {
        if (!processedContent) return; // Don't run if the content hasn't been processed yet

        const codeBlocks = document.querySelectorAll('.copy-code-btn');

        const handleCopyClick = async (e: Event) => {
            const button = e.currentTarget as HTMLButtonElement;
            // Retrieve the code stored in the data-code attribute.
            // Decode HTML entities before copying
            const encodedCode = button.getAttribute('data-code');
            if (encodedCode) {
                const textarea = document.createElement('textarea');
                textarea.innerHTML = encodedCode; // Let the browser decode entities
                const code = textarea.value;

                try {
                    await navigator.clipboard.writeText(code.trim());
                    button.classList.add('copied');
                    setTimeout(() => {
                        button.classList.remove('copied');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy code to clipboard:', err);
                }
            }
        };

        codeBlocks.forEach(button => {
            button.addEventListener('click', handleCopyClick);
        });

        // --- Placeholder for Syntax Highlighting ---
        // Add integration with Prism.js or highlight.js here if needed
        // e.g., if (typeof Prism !== 'undefined') { Prism.highlightAllUnder(document.querySelector('.article-content-wrapper')); }
        const codeElements = document.querySelectorAll('.article-content-wrapper pre code');
        codeElements.forEach(el => {
            el.classList.add('syntax-highlighted'); // Class for basic CSS styling
            // If using a library, trigger highlighting here
        });

        // Cleanup function
        return () => {
            codeBlocks.forEach(button => {
                button.removeEventListener('click', handleCopyClick);
            });
        };
    }, [processedContent]); // Re-run this effect if the processed content changes

    if (loading) {
        return (
            <div className="min-h-screen bg-dark flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neon"></div>
            </div>
        );
    }

    if (!post) {
        return (
            <div className="min-h-screen bg-dark">
                <Header />
                <div className="container mx-auto px-4 py-20 text-center">
                    {/* Use t function for translatable strings */}
                    <h1 className="text-3xl font-bold mb-6">{t('blogPostNotFound.title', 'Blog Post Not Found')}</h1>
                    <p className="text-gray-400 mb-8">{t('blogPostNotFound.message', "The blog post you're looking for doesn't exist or has been removed.")}</p>
                    <Button asChild>
                        <Link to="/blog" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" /> {t('blogPostNotFound.backButton', 'Back to Blog')}
                        </Link>
                    </Button>
                </div>
                <Footer />
            </div>
        );
    }

    // Get title and excerpt for the current language, fallback to English
    const postTitle = post.title[currentLang] || post.title.en;
    // const postExcerpt = post.excerpt[currentLang] || post.excerpt.en; // Excerpt not used directly here, but available

    return (
        // Add dir attribute based on language for correct text direction (LTR/RTL)
        <div className="min-h-screen bg-dark pt-16" dir={currentLang === 'ar' ? 'rtl' : 'ltr'}>
            <Header />

            <main className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    {/* Inline styles for code blocks & copy button (unchanged) */}
                    <style>{`
                      /* --- Code Block Container --- */
                      .code-block {
                        position: relative; /* Needed for absolute positioning of the button */
                        margin: 1.5rem 0;
                      }

                      /* --- Copy Button Styling --- */
                      .copy-code-btn {
                        position: absolute;
                        top: 0.75rem; /* Adjust as needed */
                        inset-inline-end: 0.75rem; /* Use logical properties for RTL support */
                        background: rgba(255, 255, 255, 0.1); /* Semi-transparent background */
                        border-radius: 4px;
                        padding: 0.25rem;
                        opacity: 0; /* Hidden by default */
                        transition: opacity 0.2s ease;
                        width: 30px; /* Fixed size */
                        height: 30px; /* Fixed size */
                        border: none; /* Remove default button border */
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      }

                      /* --- Show Button on Hovering Code Block --- */
                      .code-block:hover .copy-code-btn {
                        opacity: 1; /* Fade in on hover */
                      }

                      /* --- Copy Button Icon (Default: Copy Icon) --- */
                      /* Uses an inline SVG encoded as a data URI for the background image */
                      .copy-code-btn::before {
                        content: '';
                        position: absolute; /* Ensure it's layered correctly if needed */
                        width: 16px; /* Icon size */
                        height: 16px; /* Icon size */
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='9' y='9' width='13' height='13' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'%3E%3C/path%3E%3C/svg%3E");
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        transition: background-image 0.1s ease-in-out; /* Add transition for icon change */
                      }

                      /* --- Copy Button Icon (Copied State: Checkmark Icon) --- */
                      /* Changes the icon when the 'copied' class is added */
                      .copy-code-btn.copied::before {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2300ffdc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E"); /* Neon checkmark */
                      }

                       /* --- Styling for the <pre> element --- */
                      pre {
                        background: rgba(0, 0, 0, 0.3) !important; /* Darker background for code */
                        border-radius: 8px !important;
                        padding: 1.25rem !important; /* More padding */
                        padding-top: 2.5rem !important; /* Add padding top to avoid overlap with button */
                        overflow-x: auto !important; /* Enable horizontal scrolling */
                        border: 1px solid rgba(255, 255, 255, 0.1) !important; /* Subtle border */
                        direction: ltr !important; /* Force LTR for code blocks */
                        text-align: left !important; /* Force left align for code blocks */
                      }

                      /* --- Styling for the <code> element inside <pre> --- */
                      pre code {
                        font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace !important;
                        font-size: 0.9rem !important;
                        line-height: 1.6 !important;
                        color: #f8f8f2; /* Default code color */
                        background: none !important;
                        padding: 0 !important;
                        white-space: pre !important; /* Preserve whitespace and line breaks */
                      }

                      /* --- Basic Placeholder Syntax Highlighting (unchanged) --- */
                      .syntax-highlighted .keyword { color: #ff79c6; }
                      .syntax-highlighted .string { color: #f1fa8c; }
                      .syntax-highlighted .comment { color: #6272a4; font-style: italic; }
                      .syntax-highlighted .function { color: #50fa7b; }
                      .syntax-highlighted .operator { color: #ff79c6; }
                      .syntax-highlighted .tag { color: #ff79c6; }
                      .syntax-highlighted .attr-name { color: #50fa7b; }
                      .syntax-highlighted .attr-value { color: #f1fa8c; }
                      .syntax-highlighted .punctuation { color: #f8f8f2; }
                      .syntax-highlighted .number { color: #bd93f9; }
                      .syntax-highlighted .property { color: #8be9fd; }

                      /* --- Specific styles for Arabic --- */
                      [dir="rtl"] .font-cairo {
                        font-family: 'Cairo', sans-serif; /* Ensure Cairo font is loaded */
                      }
                      [dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
                         font-family: 'Cairo', sans-serif; /* Apply Cairo to headings in RTL */
                      }
                      [dir="rtl"] .flex.items-center.gap-2 > svg { /* Adjust icon position for RTL */
                         margin-left: 0.5rem;
                         margin-right: 0;
                      }
                      [dir="rtl"] .flex.items-center.gap-1 > svg { /* Adjust icon position for RTL */
                         margin-left: 0.25rem; /* equivalent to gap-1 */
                         margin-right: 0;
                      }
                     `}</style>

                    {/* Back Button */}
                    <Button
                        variant="ghost"
                        className="mb-8 text-gray-400 hover:text-white"
                        asChild
                    >
                        <Link to="/blog" className="flex items-center gap-2">
                            {/* Adjust icon position based on direction */}
                            {currentLang !== 'ar' && <ArrowLeft className="h-4 w-4" />}
                            {t('blogPostNotFound.backButton', 'Back to Blog')} {/* Reuse translation key */}
                            {currentLang === 'ar' && <ArrowLeft className="h-4 w-4 transform scale-x-[-1]" />} {/* Flip arrow for RTL */}
                        </Link>
                    </Button>

                    {/* Article Header */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="flex flex-wrap gap-2 mb-4">
                            <Badge variant="outline" className="border-neon text-neon bg-neon/5 px-3">
                                {post.category} {/* Category might need translation */}
                            </Badge>
                            {post.tags.map((tag: string) => (
                                <Badge
                                    key={tag}
                                    variant="outline"
                                    className="bg-transparent border-gray-700 text-gray-400"
                                >
                                    {tag} {/* Tags might need translation */}
                                </Badge>
                            ))}
                        </div>

                        {/* Display translated title */}
                        <h1 className={`text-4xl md:text-5xl font-bold mb-6 ${currentLang === 'ar' ? 'font-cairo' : ''}`}>{postTitle}</h1>

                        <div className="flex items-center gap-4 text-gray-400 mb-8">
                            <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" /> {post.date}
                            </div>
                            <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" /> {post.readTime} {/* Read time might change based on language */}
                            </div>
                        </div>
                    </motion.div>

                    {/* Article Content Wrapper (for potential syntax highlighting scope) */}
                    <div
                        className="prose prose-invert max-w-none article-content-wrapper" // Add prose styles if using Tailwind Typography
                        // Use the processed content which now includes copy buttons
                        dangerouslySetInnerHTML={{ __html: processedContent }}
                    />

                </div>
            </main>

            <Footer />
        </div>
    );
}