<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <title>Altayeb | Portfolio</title>
    <meta name="description" content="Altayeb - Portfolio site showcasing creative works and projects" />
    <meta name="author" content="Altayeb" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Altayeb | Portfolio" />
    <meta property="og:description" content="Specialized in digital art, creative design, and unique web experiences." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/favcon.png" />
    <meta property="og:image:alt" content="Altayeb Portfolio" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Altayeb | Portfolio" />
    <meta name="twitter:description" content="Specialized in digital art, creative design, and unique web experiences." />
    <meta name="twitter:image" content="/favcon.png" />
    
    <meta name="theme-color" content="#111111" />
    
    <!-- Favicon -->
    <link rel="icon" href="/favcon.png" type="image/png">
    <link rel="apple-touch-icon" href="/favcon.png">
    
    <!-- Preload Largest Contentful Paint image for projects page -->
    <link rel="preload" href="/projects/optimized/Vusto.webp" as="image" type="image/webp">
    
    <!-- Preconnect to required origins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Optimize Google Fonts loading -->
    <link href="https://fonts.googleapis.com/css2?family=Fustat:wght@200..800&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Fustat:wght@200..800&display=swap" rel="stylesheet">
    </noscript>
    
    <!-- Inline critical CSS to avoid render blocking -->
    <style>
      /* Critical styles for initial render */
      html {
        overflow-x: hidden;
        scroll-behavior: smooth;
      }

      body {
        margin: 0;
        padding: 0;
        background-color: #111111;
        color: #ffffff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow-x: hidden;
        min-height: 100vh;
        min-height: 100dvh;
        width: 100%;
        max-width: 100vw;
        box-sizing: border-box;
      }

      /* Prevent layout shifts */
      #root {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
        box-sizing: border-box;
      }

      /* Hide body until CSS is loaded to prevent FOUC */
      body:not(.loaded) {
        visibility: hidden;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    
    <!-- Make body visible once loaded -->
    <script>
      document.body.classList.add('loaded');
    </script>
    
    <!-- Defer non-critical scripts -->
    <script src="https://cdn.gpteng.co/gptengineer.js" defer></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
