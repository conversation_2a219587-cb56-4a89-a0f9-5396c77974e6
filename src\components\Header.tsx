import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useTranslation } from 'react-i18next';

const navItems = [
  { key: "home", href: "/" },
  { key: "projects", href: "/projects" },
  { key: "blog", href: "/blog" },
  { key: "contact", href: "/contact" },
];

export default function Header() {
  const { t, i18n } = useTranslation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const lastScrollY = useRef(0);
  const isRTL = i18n.language === 'ar';
  
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Determine if scrolled past threshold - make this a bit higher for better effect
      setIsScrolled(currentScrollY > 80);
      
      // Hide header when scrolling down, show when scrolling up
      if (currentScrollY > lastScrollY.current && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      lastScrollY.current = currentScrollY;
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en';
    i18n.changeLanguage(newLang);
  };

  return (
    <header 
      className={`fixed left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-dark/95 backdrop-blur-lg shadow-lg' : 'bg-transparent'
      } ${
        isVisible || isHovering ? 'top-0' : '-top-20'
      }`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className="container mx-auto px-4 py-3 sm:py-4">
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'} justify-between`}>
          {/* Logo */}
          <Link to="/" className={`text-xl sm:text-2xl font-bold ${!isScrolled && 'drop-shadow-[0_0_3px_rgba(0,0,0,0.7)]'}`}>
            {isRTL ? (
              <span className="text-gradient font-rakkas text-3xl">الطيب</span>
            ) : (
              <span className="text-gradient font-limelight">Altayeb</span>
            )}
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <nav className={`flex items-center ${isRTL ? 'rtl-navbar space-x-0 space-x-reverse' : 'space-x-8'}`}>
              {isRTL ? 
                [
                  <Button 
                    key="lang-btn"
                    onClick={toggleLanguage} 
                    variant="outline" 
                    className={`border-neon text-neon hover:bg-neon hover:text-dark btn-glow ${!isScrolled && 'bg-dark/30'}`}
                  >
                    {t('header.language')}
                  </Button>,
                  ...[...navItems].reverse().map((item) => (
                    <Link
                      key={item.key}
                      to={item.href}
                      className={`relative text-sm font-medium ${isScrolled ? 'text-gray-200' : 'text-white drop-shadow-[0_0_3px_rgba(0,0,0,0.5)]'} hover:text-neon transition-colors
                        before:content-[''] before:absolute before:-bottom-1 ${isRTL ? 'before:right-0' : 'before:left-0'} before:w-full before:h-px 
                        before:bg-neon before:transform before:scale-x-0 before:transition-transform before:duration-300
                        hover:before:scale-x-100`}
                    >
                      {t(`header.${item.key}`)}
                    </Link>
                  ))
                ]
              : 
                [
                  ...navItems.map((item) => (
                    <Link
                      key={item.key}
                      to={item.href}
                      className={`relative text-sm font-medium ${isScrolled ? 'text-gray-200' : 'text-white drop-shadow-[0_0_3px_rgba(0,0,0,0.5)]'} hover:text-neon transition-colors
                        before:content-[''] before:absolute before:-bottom-1 ${isRTL ? 'before:right-0' : 'before:left-0'} before:w-full before:h-px 
                        before:bg-neon before:transform before:scale-x-0 before:transition-transform before:duration-300
                        hover:before:scale-x-100`}
                    >
                      {t(`header.${item.key}`)}
                    </Link>
                  )),
                  <Button 
                    key="lang-btn"
                    onClick={toggleLanguage} 
                    variant="outline" 
                    className={`border-neon text-neon hover:bg-neon hover:text-dark btn-glow ${!isScrolled && 'bg-dark/30'}`}
                  >
                    {t('header.language')}
                  </Button>
                ]
              }
            </nav>
          </div>

          {/* Mobile Navigation */}
          <div className={`md:hidden flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
            <Button 
              onClick={toggleLanguage} 
              variant="outline" 
              size="sm"
              className={`${isRTL ? 'ml-2' : 'mr-2'} ${!isScrolled && 'bg-dark/30'} border-neon text-neon hover:bg-neon hover:text-dark btn-glow text-xs px-2 py-1 h-8`}
            >
              {t('header.language')}
            </Button>
            
            <Sheet>
              <SheetTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className={`h-9 w-9 rounded-md ${!isScrolled && 'bg-dark/30'} hover:bg-neon/10`}
                >
                  <Menu className="h-5 w-5 text-white hover:text-neon transition-colors" />
                </Button>
              </SheetTrigger>
              <SheetContent 
                side={isRTL ? 'right' : 'left'} 
                className="bg-dark/95 backdrop-blur-lg border-neon/20 w-[280px] p-0"
              >
                <div className="flex flex-col h-full">
                  <div className={`p-6 border-b border-white/10 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <span className="text-xl font-bold">
                      {isRTL ? (
                        <span className="text-gradient font-rakkas text-2xl">الطيب</span>
                      ) : (
                        <span className="text-gradient font-limelight">Altayeb</span>
                      )}
                    </span>
                  </div>
                  
                  <nav className={`flex flex-col p-6 flex-grow ${isRTL ? 'text-right' : 'text-left'}`}>
                    {isRTL ? 
                      [...navItems].reverse().map((item, index) => (
                        <Link
                          key={item.key}
                          to={item.href}
                          className="py-3 text-lg font-medium text-gray-200 hover:text-neon transition-colors border-b border-white/5 last:border-0"
                        >
                          <span className={`text-neon opacity-70 ${isRTL ? 'ml-2' : 'mr-2'}`}>{index + 1}.</span>
                          {t(`header.${item.key}`)}
                        </Link>
                      ))
                    :
                      navItems.map((item, index) => (
                        <Link
                          key={item.key}
                          to={item.href}
                          className="py-3 text-lg font-medium text-gray-200 hover:text-neon transition-colors border-b border-white/5 last:border-0"
                        >
                          <span className={`text-neon opacity-70 ${isRTL ? 'ml-2' : 'mr-2'}`}>{index + 1}.</span>
                          {t(`header.${item.key}`)}
                        </Link>
                      ))
                    }
                  </nav>
                  
                  <div className="p-6 mt-auto border-t border-white/10">
                    <div className="flex gap-4 justify-center">
                      <a 
                        href="https://github.com/altyb" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="p-2 rounded-full border border-white/20 hover:border-neon hover:text-neon transition-colors"
                        aria-label="GitHub"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>
                      </a>
                      <a 
                        href="https://linkedin.com/in/altyb" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="p-2 rounded-full border border-white/20 hover:border-neon hover:text-neon transition-colors"
                        aria-label="LinkedIn"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg>
                      </a>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}


