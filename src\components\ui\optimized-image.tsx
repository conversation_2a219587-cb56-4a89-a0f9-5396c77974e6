import React, { useState, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  width?: number;
  height?: number;
  onClick?: () => void;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
}

export function OptimizedImage({
  src,
  alt,
  className = '',
  sizes = '100vw',
  width,
  height,
  onClick,
  priority = false,
  loading = 'lazy',
}: OptimizedImageProps) {
  const [imageSrc, setImageSrc] = useState<string>(src);
  const [hasError, setHasError] = useState<boolean>(false);

  // Extract the file name and path for different formats
  const getOptimizedSrc = (originalSrc: string, format: string) => {
    if (!originalSrc.startsWith('/')) return originalSrc;
    
    const srcWithoutLeadingSlash = originalSrc.startsWith('/') ? originalSrc.slice(1) : originalSrc;
    const lastDotIndex = srcWithoutLeadingSlash.lastIndexOf('.');
    
    if (lastDotIndex === -1) return originalSrc;
    
    const basePath = srcWithoutLeadingSlash.substring(0, lastDotIndex);
    const filePath = basePath.split('/');
    const fileName = filePath.pop() || '';
    
    return `/projects/optimized/${fileName}.${format}`;
  };

  // Handle image loading error
  const handleError = () => {
    if (!hasError) {
      setHasError(true);
      setImageSrc(src); // Fall back to original source
    }
  };

  useEffect(() => {
    // Reset error state when src changes
    setHasError(false);
    setImageSrc(src);
  }, [src]);

  // Only optimize images from the projects directory
  const shouldOptimize = src.includes('/projects/') && src.match(/\.(png|jpe?g)$/i);
  
  if (!shouldOptimize) {
    return (
      <img
        src={src}
        alt={alt}
        className={className}
        loading={loading}
        onClick={onClick}
      />
    );
  }

  const webpSrc = getOptimizedSrc(src, 'webp');
  const avifSrc = getOptimizedSrc(src, 'avif');
  const fallbackSrc = hasError ? src : imageSrc;

  // Use smaller sized images for different breakpoints
  const getResponsiveSrc = (format: string) => {
    const srcPath = getOptimizedSrc(src, format);
    const basePath = srcPath.substring(0, srcPath.lastIndexOf('.'));
    
    return {
      small: `${basePath.replace('.' + format, '')}-400.${format}`,
      medium: `${basePath.replace('.' + format, '')}-800.${format}`,
      large: `${basePath.replace('.' + format, '')}-1200.${format}`,
      original: srcPath
    };
  };

  const webpSources = getResponsiveSrc('webp');

  return (
    <picture>
      {/* AVIF format support */}
      <source
        srcSet={avifSrc}
        type="image/avif"
      />
      
      {/* WebP format with responsive sizes */}
      <source
        srcSet={`${webpSources.small} 400w, ${webpSources.medium} 800w, ${webpSources.large} 1200w, ${webpSources.original} 1920w`}
        sizes={sizes}
        type="image/webp"
      />
      
      {/* Original fallback image */}
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        loading={priority ? 'eager' : loading}
        onError={handleError}
        width={width}
        height={height}
        onClick={onClick}
      />
    </picture>
  );
} 