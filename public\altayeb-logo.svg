<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00FFF7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main logo shape - stylized "A" for Altayeb -->
  <circle cx="16" cy="16" r="15" fill="url(#logoGradient)" opacity="0.1" filter="url(#glow)"/>
  
  <!-- Letter "A" design -->
  <path d="M16 6L10 24H13L14.5 20H17.5L19 24H22L16 6Z" fill="url(#logoGradient)" stroke="url(#logoGradient)" stroke-width="0.5"/>
  <path d="M15 16H17L16 13L15 16Z" fill="#121212"/>
  
  <!-- Accent dots for modern touch -->
  <circle cx="8" cy="8" r="1.5" fill="url(#logoGradient)" opacity="0.6"/>
  <circle cx="24" cy="24" r="1.5" fill="url(#logoGradient)" opacity="0.6"/>
  
  <!-- Code brackets for developer identity -->
  <path d="M6 12L4 16L6 20" stroke="url(#logoGradient)" stroke-width="1.5" fill="none" opacity="0.7"/>
  <path d="M26 12L28 16L26 20" stroke="url(#logoGradient)" stroke-width="1.5" fill="none" opacity="0.7"/>
</svg>
