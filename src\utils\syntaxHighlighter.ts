/**
 * Safe syntax highlighting utility
 * Provides syntax highlighting without using dangerouslySetInnerHTML
 */

export interface HighlightedToken {
  type: 'keyword' | 'string' | 'comment' | 'number' | 'tag' | 'attribute' | 'value' | 'text';
  content: string;
}

/**
 * Safely tokenize and highlight code without HTML injection
 */
export function tokenizeCode(code: string, language: 'html' | 'css' | 'javascript'): HighlightedToken[] {
  const tokens: HighlightedToken[] = [];
  
  switch (language) {
    case 'html':
      return tokenizeHTML(code);
    case 'css':
      return tokenizeCSS(code);
    case 'javascript':
      return tokenizeJavaScript(code);
    default:
      return [{ type: 'text', content: code }];
  }
}

function tokenizeHTML(code: string): HighlightedToken[] {
  const tokens: HighlightedToken[] = [];
  const htmlRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^<>]*>/g;
  const attributeRegex = /(\w+)=["']([^"']*)["']/g;
  
  let lastIndex = 0;
  let match;
  
  while ((match = htmlRegex.exec(code)) !== null) {
    // Add text before tag
    if (match.index > lastIndex) {
      const textContent = code.slice(lastIndex, match.index);
      if (textContent.trim()) {
        tokens.push({ type: 'text', content: textContent });
      }
    }
    
    const fullTag = match[0];
    const tagName = match[1];
    
    // Parse tag content
    tokens.push({ type: 'tag', content: '<' });
    if (fullTag.startsWith('</')) {
      tokens.push({ type: 'tag', content: '/' });
    }
    tokens.push({ type: 'tag', content: tagName });
    
    // Parse attributes
    const attributesPart = fullTag.slice(fullTag.indexOf(tagName) + tagName.length, -1);
    let attrMatch;
    let attrLastIndex = 0;
    
    while ((attrMatch = attributeRegex.exec(attributesPart)) !== null) {
      if (attrMatch.index > attrLastIndex) {
        const spaceBefore = attributesPart.slice(attrLastIndex, attrMatch.index);
        if (spaceBefore.trim()) {
          tokens.push({ type: 'text', content: spaceBefore });
        }
      }
      
      tokens.push({ type: 'attribute', content: attrMatch[1] });
      tokens.push({ type: 'text', content: '="' });
      tokens.push({ type: 'value', content: attrMatch[2] });
      tokens.push({ type: 'text', content: '"' });
      
      attrLastIndex = attrMatch.index + attrMatch[0].length;
    }
    
    if (attrLastIndex < attributesPart.length) {
      tokens.push({ type: 'text', content: attributesPart.slice(attrLastIndex) });
    }
    
    tokens.push({ type: 'tag', content: '>' });
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < code.length) {
    tokens.push({ type: 'text', content: code.slice(lastIndex) });
  }
  
  return tokens;
}

function tokenizeCSS(code: string): HighlightedToken[] {
  const tokens: HighlightedToken[] = [];
  const cssRegex = /(\/\*[\s\S]*?\*\/)|([.#]?[a-zA-Z-]+)\s*\{|([a-zA-Z-]+)\s*:|([^{}:;]+);?/g;
  
  let lastIndex = 0;
  let match;
  
  while ((match = cssRegex.exec(code)) !== null) {
    if (match.index > lastIndex) {
      const textContent = code.slice(lastIndex, match.index);
      if (textContent.trim()) {
        tokens.push({ type: 'text', content: textContent });
      }
    }
    
    if (match[1]) {
      // Comment
      tokens.push({ type: 'comment', content: match[1] });
    } else if (match[2]) {
      // Selector
      tokens.push({ type: 'tag', content: match[2] });
      tokens.push({ type: 'text', content: ' {' });
    } else if (match[3]) {
      // Property
      tokens.push({ type: 'attribute', content: match[3] });
      tokens.push({ type: 'text', content: ':' });
    } else if (match[4]) {
      // Value
      tokens.push({ type: 'value', content: match[4] });
    }
    
    lastIndex = match.index + match[0].length;
  }
  
  if (lastIndex < code.length) {
    tokens.push({ type: 'text', content: code.slice(lastIndex) });
  }
  
  return tokens;
}

function tokenizeJavaScript(code: string): HighlightedToken[] {
  const tokens: HighlightedToken[] = [];
  const keywords = ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'document', 'window', 'addEventListener', 'setTimeout'];
  const jsRegex = new RegExp(`\\b(${keywords.join('|')})\\b|('.*?'|".*?")|(\\/\\/.*$)|(\\d+)`, 'gm');
  
  let lastIndex = 0;
  let match;
  
  while ((match = jsRegex.exec(code)) !== null) {
    if (match.index > lastIndex) {
      const textContent = code.slice(lastIndex, match.index);
      if (textContent.trim()) {
        tokens.push({ type: 'text', content: textContent });
      }
    }
    
    if (match[1]) {
      // Keyword
      tokens.push({ type: 'keyword', content: match[1] });
    } else if (match[2]) {
      // String
      tokens.push({ type: 'string', content: match[2] });
    } else if (match[3]) {
      // Comment
      tokens.push({ type: 'comment', content: match[3] });
    } else if (match[4]) {
      // Number
      tokens.push({ type: 'number', content: match[4] });
    }
    
    lastIndex = match.index + match[0].length;
  }
  
  if (lastIndex < code.length) {
    tokens.push({ type: 'text', content: code.slice(lastIndex) });
  }
  
  return tokens;
}

/**
 * Get CSS class for token type
 */
export function getTokenClassName(type: HighlightedToken['type']): string {
  switch (type) {
    case 'keyword':
      return 'text-purple-400';
    case 'string':
      return 'text-green-300';
    case 'comment':
      return 'text-gray-500';
    case 'number':
      return 'text-blue-400';
    case 'tag':
      return 'text-purple-400';
    case 'attribute':
      return 'text-yellow-300';
    case 'value':
      return 'text-green-300';
    default:
      return 'text-gray-300';
  }
}
