import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import AboutSection from "@/components/AboutSection";
import SkillsSection from "@/components/SkillsSection";
import Footer from "@/components/Footer";
import { SEOHead } from "@/components/SEOHead";
import { useTranslation } from 'react-i18next';

const Index = () => {
  const { i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const homeStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": isRTL ? "الطيب - مطور ويب متخصص في الذكاء الاصطناعي" : "Altayeb - AI-Enhanced Full Stack Developer",
    "url": "https://altyb.netlify.app",
    "description": isRTL
      ? "مطور ويب متخصص في الذكاء الاصطناعي وهندسة الأوامر. أقوم ببناء تطبيقات ويب حديثة ومتاجر إلكترونية متقدمة."
      : "AI-Enhanced Full Stack Developer specializing in prompt engineering and modern web applications.",
    "author": {
      "@type": "Person",
      "name": "Altayeb"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://altyb.netlify.app/blog?search={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <div className="min-h-screen bg-dark">
      <SEOHead
        url="/"
        keywords={isRTL
          ? ['مطور ويب', 'الذكاء الاصطناعي', 'هندسة الأوامر', 'React', 'TypeScript', 'متاجر إلكترونية']
          : ['web developer', 'AI developer', 'prompt engineering', 'React', 'TypeScript', 'e-commerce']
        }
        structuredData={homeStructuredData}
      />
      <Header />
      <main>
        <HeroSection />
        <AboutSection />
        <SkillsSection />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
