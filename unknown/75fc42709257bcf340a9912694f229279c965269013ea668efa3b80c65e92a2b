import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

interface LogoProps {
  className?: string;
  showText?: boolean;
}

export function Logo({ className = '', showText = true }: LogoProps) {
  const { i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <Link to="/" className={`flex items-center ${className}`}>
      <img 
        src="/altayeb-logo.svg" 
        alt={isRTL ? "الطيب" : "Altayeb"} 
        className="w-8 h-8 mr-2" 
      />
      {showText && (
        isRTL ? (
          <span className="text-white font-rakkas text-2xl">الطيب</span>
        ) : (
          <span className="text-gradient text-xl font-bold">Altayeb</span>
        )
      )}
    </Link>
  );
}

export default Logo; 