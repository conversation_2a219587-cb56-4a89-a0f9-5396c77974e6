import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

// List of RTL languages
const RTL_LANGUAGES = ["ar", "he", "fa", "ur"];

export const useDirection = () => {
  const { i18n } = useTranslation();
  const [isRTL, setIsRTL] = useState(RTL_LANGUAGES.includes(i18n.language));

  useEffect(() => {
    const dir = RTL_LANGUAGES.includes(i18n.language) ? "rtl" : "ltr";
    document.documentElement.dir = dir;
    document.documentElement.lang = i18n.language;
    setIsRTL(RTL_LANGUAGES.includes(i18n.language));
  }, [i18n.language]);

  return { isRTL };
}; 