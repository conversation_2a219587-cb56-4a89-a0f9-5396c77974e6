/**
 * Error handling utilities for better error management
 */

export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  context?: Record<string, any>;
}

/**
 * Creates a standardized error object
 */
export function createError(
  message: string,
  code?: string,
  statusCode?: number,
  context?: Record<string, any>
): AppError {
  const error = new Error(message) as AppError;
  error.code = code;
  error.statusCode = statusCode;
  error.context = context;
  return error;
}

/**
 * Safely executes an async function and handles errors
 */
export async function safeAsync<T>(
  fn: () => Promise<T>,
  fallback?: T
): Promise<{ data: T | null; error: AppError | null }> {
  try {
    const data = await fn();
    return { data, error: null };
  } catch (err) {
    const error = err instanceof Error ? err as AppError : createError('Unknown error occurred');
    console.error('Async operation failed:', error);
    
    return { 
      data: fallback || null, 
      error 
    };
  }
}

/**
 * Safely executes a synchronous function and handles errors
 */
export function safeSync<T>(
  fn: () => T,
  fallback?: T
): { data: T | null; error: AppError | null } {
  try {
    const data = fn();
    return { data, error: null };
  } catch (err) {
    const error = err instanceof Error ? err as AppError : createError('Unknown error occurred');
    console.error('Sync operation failed:', error);
    
    return { 
      data: fallback || null, 
      error 
    };
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryAsync<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Debounced error handler to prevent spam
 */
export class DebouncedErrorHandler {
  private timeouts: Map<string, NodeJS.Timeout> = new Map();
  
  handle(
    key: string,
    error: Error,
    handler: (error: Error) => void,
    delay: number = 1000
  ) {
    // Clear existing timeout for this key
    const existingTimeout = this.timeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    
    // Set new timeout
    const timeout = setTimeout(() => {
      handler(error);
      this.timeouts.delete(key);
    }, delay);
    
    this.timeouts.set(key, timeout);
  }
  
  clear(key?: string) {
    if (key) {
      const timeout = this.timeouts.get(key);
      if (timeout) {
        clearTimeout(timeout);
        this.timeouts.delete(key);
      }
    } else {
      // Clear all timeouts
      this.timeouts.forEach(timeout => clearTimeout(timeout));
      this.timeouts.clear();
    }
  }
}

/**
 * Global error handler instance
 */
export const globalErrorHandler = new DebouncedErrorHandler();

/**
 * Network error handler
 */
export function handleNetworkError(error: any): AppError {
  if (!navigator.onLine) {
    return createError(
      'No internet connection. Please check your network and try again.',
      'NETWORK_OFFLINE',
      0
    );
  }
  
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return createError(
      'Request timed out. Please try again.',
      'NETWORK_TIMEOUT',
      408
    );
  }
  
  if (error.response?.status >= 500) {
    return createError(
      'Server error. Please try again later.',
      'SERVER_ERROR',
      error.response.status
    );
  }
  
  if (error.response?.status === 404) {
    return createError(
      'Resource not found.',
      'NOT_FOUND',
      404
    );
  }
  
  if (error.response?.status === 403) {
    return createError(
      'Access denied.',
      'FORBIDDEN',
      403
    );
  }
  
  return createError(
    error.message || 'Network error occurred',
    'NETWORK_ERROR',
    error.response?.status || 0
  );
}

/**
 * Form validation error handler
 */
export function handleValidationError(errors: Record<string, any>): string[] {
  const messages: string[] = [];
  
  Object.entries(errors).forEach(([field, error]) => {
    if (error?.message) {
      messages.push(`${field}: ${error.message}`);
    } else if (typeof error === 'string') {
      messages.push(`${field}: ${error}`);
    }
  });
  
  return messages;
}

/**
 * Log error to external service (placeholder)
 */
export function logError(error: AppError, context?: Record<string, any>) {
  // In production, send to error reporting service like Sentry
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error, { extra: context });
    console.error('Production error:', error, context);
  } else {
    console.error('Development error:', error, context);
  }
}
