import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// Plugin to convert relative image paths to absolute URLs in meta tags
function absoluteMetaImageUrlsPlugin() {
  const siteUrl = process.env.SITE_URL || "https://altyb.netlify.app";
  
  return {
    name: 'absolute-meta-image-urls',
    transformIndexHtml(html: string) {
      // Replace relative image paths in og:image and twitter:image meta tags with absolute URLs
      return html.replace(
        /(content=["'])\/favcon\.png(["'])/g, 
        `$1${siteUrl}/favcon.png$2`
      );
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    absoluteMetaImageUrlsPlugin(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
