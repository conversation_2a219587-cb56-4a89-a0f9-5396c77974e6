import { useState } from 'react';
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, ArrowRight, MessageSquare, Phone, MapPin, Github, Linkedin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/hooks/use-direction";

export default function ContactSection() {
  const { t } = useTranslation();
  const { isRTL } = useDirection();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // In a real application, you would send this data to a server
    // For now, we'll simulate the form submission and provide feedback
    // You could integrate with services like EmailJS, Formspree, or your own backend
    
    // Log the form data (for demonstration purposes)
    console.log('Form submitted with data:', formData);
    
    // Simulate API call delay
    setTimeout(() => {
      toast({
        title: t("contact.messageSent"),
        description: t("contact.thankYouMessage", { name: formData.name, email: formData.email }),
      });
      
      // Reset the form
      setFormData({ name: '', email: '', message: '' });
      setIsSubmitting(false);
      
      // In a real implementation, you might redirect or show a success page
    }, 1500);
  };

  const fadeInUp = {
    hidden: { y: 60, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.7 } }
  };

  return (
    <section id="contact" className="section py-24 bg-black/40">
      <div className="container">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4 font-cairo">{t("contact.title")}</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            {t("contact.subtitle")}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-14">
          <motion.div 
            className="space-y-10"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div>
              <h3 className="text-2xl font-semibold mb-6 font-cairo">{t("contact.letsCollaborate")}</h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                {t("contact.collaborationText")}
              </p>
            </div>
            
            <div>
              <h3 className="text-2xl font-semibold mb-6 font-cairo">{t("contact.myDetails")}</h3>
              <div className="space-y-6">
                <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
                  <div className="p-3 bg-neon/10 rounded-full">
                    <Mail className="h-6 w-6 text-neon" />
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">{t("contact.emailLabel")}</p>
                    <a href="mailto:<EMAIL>" className="text-gray-100 hover:text-neon transition-colors"><EMAIL></a>
                  </div>
                </div>
                <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
                  <div className="p-3 bg-neon/10 rounded-full">
                    <Phone className="h-6 w-6 text-neon" />
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">{t("contact.phoneLabel")}</p>
                    <a href="tel:+201286888691" className="text-gray-100 hover:text-neon transition-colors">+20 ************</a>
                  </div>
                </div>
                <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
                  <div className="p-3 bg-neon/10 rounded-full">
                    <MessageSquare className="h-6 w-6 text-neon" />
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">{t("contact.whatsappLabel")}</p>
                    <a href="https://wa.me/201286888691" target="_blank" rel="noopener noreferrer" className="text-gray-100 hover:text-neon transition-colors">+20 ************</a>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-4 font-cairo">{t("contact.socialProfiles")}</h3>
              <div className="flex gap-4">
                <a 
                  href="https://github.com/altyb" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="p-3 bg-darkgray rounded-full hover:bg-neon/10 hover:text-neon transition-colors"
                  aria-label="GitHub Profile"
                >
                  <Github className="h-5 w-5" />
                </a>
                <a 
                  href="https://linkedin.com/in/altyb" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="p-3 bg-darkgray rounded-full hover:bg-neon/10 hover:text-neon transition-colors"
                  aria-label="LinkedIn Profile"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="glass p-8 md:p-10 rounded-xl backdrop-blur-xl border border-white/10 hover:border-white/20 transition-all duration-500"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2 font-cairo">
                  {t("contact.form.name")}
                </label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="bg-darkgray border-white/10 focus:border-neon focus:ring-neon h-12"
                  required
                  dir="auto"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2 font-cairo">
                  {t("contact.form.email")}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="bg-darkgray border-white/10 focus:border-neon focus:ring-neon h-12"
                  required
                  dir="ltr"
                />
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2 font-cairo">
                  {t("contact.form.message")}
                </label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  className="bg-darkgray border-white/10 focus:border-neon focus:ring-neon min-h-[150px]"
                  required
                  dir="auto"
                />
              </div>
              
              <Button 
                type="submit" 
                className="bg-neon text-dark hover:bg-neon/80 w-full btn-glow text-lg py-6 h-auto transition-all duration-300"
                disabled={isSubmitting}
              >
                {isSubmitting ? t("contact.form.sending") : t("contact.form.send")}
                {isRTL ? (
                  <ArrowRight className="mr-2 h-5 w-5 rotate-180" />
                ) : (
                  <ArrowRight className="ml-2 h-5 w-5" />
                )}
              </Button>
            </form>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
