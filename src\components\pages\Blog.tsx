import { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, ArrowRight, Calendar, Clock, Search, Tag, Bookmark, TrendingUp, Filter, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useTranslation } from "react-i18next";
import { SEOHead } from "@/components/SEOHead";

// --- UPDATED blogPosts Array ---
// This array now supports multilingual content (English and Arabic)
// It matches the structure used in the BlogPost component
const blogPosts = [
    {
        id: 3,
        title: {
            en: "Prompt Engineering for Large Language Models",
            ar: "هندسة الأوامر لنماذج اللغة الكبيرة"
        },
        excerpt: {
            en: "Learn advanced techniques for crafting effective prompts that yield optimal results from Claude, GPT, and other large language models in various applications.",
            ar: "تعلم تقنيات متقدمة لصياغة أوامر فعالة تحقق أفضل النتائج من Claude و GPT ونماذج اللغة الكبيرة الأخرى في تطبيقات متنوعة."
        },
        date: "March 10, 2025", // Keeping original date for consistency
        category: {
            en: "Prompt Engineering",
            ar: "هندسة الأوامر"
        },
        readTime: {
            en: "7 min read",
            ar: "7 دقائق قراءة"
        },
        slug: "prompt-engineering-llms",
        featured: true,
        trending: false,
        tags: ["AI", "prompt engineering", "LLMs", "natural language processing", "GPT", "Claude"]
        // No 'content' needed here as it's for the blog list view
    },
    { // New Post 1: Coding Basics
        id: 4,
        title: {
            en: "Web Development Fundamentals: HTML & CSS",
            ar: "أساسيات تطوير الويب: HTML و CSS"
        },
        excerpt: {
            en: "A beginner's guide to the building blocks of the web. Learn how HTML structures content and CSS styles it, with a simple card component example.",
            ar: "دليل المبتدئين لبنات بناء الويب. تعلم كيف يقوم HTML ببناء المحتوى وكيف يقوم CSS بتنسيقه، مع مثال لمكون بطاقة بسيط."
        },
        date: "April 27, 2025", // Using current date
        category: {
            en: "Coding",
            ar: "البرمجة"
        },
        readTime: {
            en: "6 min read",
            ar: "6 دقائق قراءة"
        },
        slug: "web-dev-fundamentals-html-css",
        featured: true, // Marked as featured
        trending: true,
        tags: ["coding", "web development", "html", "css", "frontend", "beginner"]
    },
    { // New Post 2: More Prompt Engineering
        id: 5,
        title: {
            en: "Crafting Effective Prompts: Beyond the Basics",
            ar: "صياغة الأوامر الفعالة: ما وراء الأساسيات"
        },
        excerpt: {
            en: "Dive deeper into prompt engineering with structured formats, persona adoption, and techniques for specific tasks like code generation and creative writing.",
            ar: "تعمق في هندسة الأوامر مع التنسيقات المنظمة، وتبني الشخصيات، والتقنيات للمهام المحددة مثل توليد الكود والكتابة الإبداعية."
        },
        date: "April 26, 2025",
        category: {
            en: "Prompt Engineering",
            ar: "هندسة الأوامر"
        },
        readTime: {
            en: "8 min read",
            ar: "8 دقائق قراءة"
        },
        slug: "advanced-prompt-engineering-techniques",
        featured: false,
        trending: true,
        tags: ["AI", "prompt engineering", "LLMs", "GPT", "Claude", "structured prompting", "code generation"]
    },
    { // New Post 3: Vibe Coding
        id: 6,
        title: {
            en: "What is 'Vibe Coding'? Exploring Intuition and Flow",
            ar: "ما هو 'البرمجة بالحدس'؟ استكشاف البداهة والتدفق"
        },
        excerpt: {
            en: "Beyond syntax and algorithms, 'Vibe Coding' emphasizes the feel, flow, and collaborative energy in software development. Let's explore this mindset.",
            ar: "بعيدًا عن بناء الجملة والخوارزميات، تؤكد 'البرمجة بالحدس' على الشعور والتدفق والطاقة التعاونية في تطوير البرمجيات. دعونا نستكشف هذه العقلية."
        },
        date: "April 25, 2025",
        category: {
            en: "Development Culture",
            ar: "ثقافة التطوير"
        },
        readTime: {
            en: "5 min read",
            ar: "5 دقائق قراءة"
        },
        slug: "what-is-vibe-coding",
        featured: false,
        trending: false,
        tags: ["vibe coding", "developer mindset", "coding style", "collaboration", "flow state", "software development"]
    }
];


export default function Blog() {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language || 'en';
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredPosts, setFilteredPosts] = useState(blogPosts);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  const { scrollYProgress } = useScroll();
  const opacity = useTransform(scrollYProgress, [0, 0.05], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.05], [1, 0.97]);

  // Animation variants
  const fadeInUp = {
    hidden: { y: 40, opacity: 0 },
    show: { y: 0, opacity: 1, transition: { duration: 0.6 } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  const fadeIn = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { duration: 0.8 } }
  };

  const scaleUp = {
    hidden: { scale: 0.95, opacity: 0 },
    show: { scale: 1, opacity: 1, transition: { duration: 0.5 } }
  };

  // Filter posts based on search query and category/tags
  // Updated to handle multilingual content
  useEffect(() => {
    let results = blogPosts;
    
    // Filter by search query
    if (searchQuery) {
      results = results.filter(post =>
        post.title[currentLang]?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt[currentLang]?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by main category tab (All, Featured, Trending)
    if (selectedCategory !== 'all') {
      if (selectedCategory === 'featured') {
        results = results.filter(post => post.featured);
      } else if (selectedCategory === 'trending') {
        results = results.filter(post => post.trending);
      }
      // NOTE: Filtering by specific *post.category* (like "Coding", "Prompt Engineering")
      // is not directly handled by the current Tabs setup, but could be added if needed.
      // The current tabs only handle 'all', 'featured', 'trending'.
    }

    // Filter by selected tags
    if (activeFilters.length > 0) {
      results = results.filter(post =>
        activeFilters.every(filter => post.tags.includes(filter)) // Changed from 'some' to 'every' for stricter tag filtering
      );
    }

    setFilteredPosts(results);
  }, [searchQuery, selectedCategory, activeFilters]);

  // Memoized function to get unique tags from the current set of posts
  const getPopularTags = useMemo(() => {
     const allTags = blogPosts.flatMap(post => post.tags);
     // Simple frequency count
     const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
     }, {} as Record<string, number>);
     // Sort by frequency (descending) and take top 10
     return Object.entries(tagCounts)
        .sort(([, countA], [, countB]) => countB - countA)
        .map(([tag]) => tag)
        .slice(0, 10);
  }, []);

  return (
    <div className="min-h-screen bg-dark">
      <SEOHead
        title={t('blog.title', 'Blog')}
        description={t('blog.subtitle', 'Insights on AI, web development, prompt engineering, and modern technology trends from a full-stack developer perspective.')}
        url="/blog"
        keywords={['blog', 'AI', 'web development', 'prompt engineering', 'technology', 'tutorials', 'insights']}
      />
      <Header />

      {/* Hero Section with Parallax Effect */}
      <motion.div
        style={{ opacity, scale }}
        className="relative h-[60vh] min-h-[500px] flex items-center justify-center overflow-hidden mt-0 pt-16"
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-transparent z-10"></div>
        {/* Consider updating the hero background image if needed */}
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=1470')] bg-cover bg-center opacity-20"></div>

        <motion.div
          className="container relative z-20 text-center max-w-3xl mx-auto px-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <motion.h1
            className="text-5xl md:text-7xl font-bold mb-6 font-cairo"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            {t('blog.latestInsights', 'Latest')} <span className="text-gradient">{t('blog.latestInsights', 'Insights')}</span>
          </motion.h1>
          <motion.p
            className="text-gray-300 text-lg md:text-xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 0.4 }}
          >
            {/* Updated description to reflect new content focus */}
            {t('blog.articlesDescription', 'Articles on Prompt Engineering, Web Development, Coding Culture, and more.')}
          </motion.p>
        </motion.div>
      </motion.div>

      <main className="relative z-10 -mt-16">
        <motion.div
          className="container mx-auto px-4 sm:px-6 lg:px-8 pb-24"
          initial="hidden"
          animate="show"
          variants={fadeIn}
        >
          {/* Search and Filter Bar */}
          <motion.div
            className="bg-dark/80 backdrop-blur-lg border border-white/10 rounded-xl p-6 mb-12 shadow-[0_8px_30px_rgb(0,0,0,0.12)]"
            variants={scaleUp}
          >
            <div className="flex flex-col md:flex-row gap-4 md:items-center mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder={t('blog.searchPlaceholder', 'Search articles...')}
                  className="pl-10 bg-black/30 border-white/5 focus-visible:ring-neon"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Tabs defaultValue="all" className="w-full md:w-auto" onValueChange={setSelectedCategory}>
                <TabsList className="bg-black/30 p-1">
                  <TabsTrigger value="all" className="data-[state=active]:bg-neon data-[state=active]:text-dark">
                    {t('blog.all', 'All')}
                  </TabsTrigger>
                  <TabsTrigger value="featured" className="data-[state=active]:bg-neon data-[state=active]:text-dark">
                    <Bookmark className="h-4 w-4 mr-1" /> {t('blog.featured', 'Featured')}
                  </TabsTrigger>
                  <TabsTrigger value="trending" className="data-[state=active]:bg-neon data-[state=active]:text-dark">
                    <TrendingUp className="h-4 w-4 mr-1" /> {t('blog.trending', 'Trending')}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </motion.div>

          {/* Popular Tags */}
          <motion.div
            className="mb-10"
            variants={fadeInUp}
          >
            <div className="flex items-center gap-3 mb-4">
              <Tag className="h-5 w-5 text-neon" />
              <h3 className="text-xl font-semibold">{t('blog.popularTags', 'Popular Tags')}</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {/* Dynamically generate popular tags from the current posts */}
              {getPopularTags.map(tag => (
                <Badge
                  key={tag}
                  variant="outline"
                  className={`cursor-pointer transition-all duration-300 px-3 py-1 focus:outline-none focus:ring-2 focus:ring-neon focus:ring-offset-2 focus:ring-offset-dark ${activeFilters.includes(tag)
                      ? 'bg-neon text-dark border-neon font-medium' // Highlight active tag
                      : 'bg-dark/70 border-white/10 text-gray-300 hover:bg-neon/10 hover:text-neon hover:border-neon/30'}`
                  }
                  onClick={() => {
                    // Toggle tag filter
                    if (activeFilters.includes(tag)) {
                      setActiveFilters(prev => prev.filter(t => t !== tag));
                    } else {
                      setActiveFilters(prev => [...prev, tag]);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      if (activeFilters.includes(tag)) {
                        setActiveFilters(prev => prev.filter(t => t !== tag));
                      } else {
                        setActiveFilters(prev => [...prev, tag]);
                      }
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-pressed={activeFilters.includes(tag)}
                  aria-label={`${activeFilters.includes(tag) ? 'Remove' : 'Add'} ${tag} filter`}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </motion.div>

          <AnimatePresence>
            {activeFilters.length > 0 && (
              <motion.div
                className="flex flex-wrap gap-2 mt-4 items-center" // Added items-center
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center mr-2">
                  <Filter className="h-4 w-4 mr-1 text-gray-400" />
                  <span className="text-sm text-gray-400">{t('blog.activeFilters', 'Active Filters')}</span>
                </div>
                {activeFilters.map(filter => (
                  <Badge
                    key={filter}
                    variant="outline"
                    className="bg-neon/10 text-neon border-neon/30 flex items-center gap-1 px-3 py-1 text-sm" // Slightly larger text
                  >
                    {filter}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-white" // Added hover effect
                      onClick={() => setActiveFilters(prev => prev.filter(f => f !== filter))}
                    />
                  </Badge>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-400 hover:text-white h-6 px-2 ml-2"
                  onClick={() => {
                    setActiveFilters([]);
                  }}
                >
                  {t('blog.clearFilters', 'Clear filters')}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Blog Posts Grid */}
          <AnimatePresence mode="wait">
            {filteredPosts.length > 0 ? (
              <motion.div
                key="posts"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
                initial="hidden"
                animate="show"
                exit={{ opacity: 0 }}
                variants={staggerContainer}
              >
                {/* Map over the filteredPosts using the updated data */}
                {filteredPosts.map((post) => (
                  <BlogPostCard key={post.id} post={post} variants={fadeInUp} />
                ))}
              </motion.div>
            ) : (
              <motion.div
                key="no-results"
                className="text-center py-20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <h3 className="text-2xl font-semibold mb-4">{t('blog.noResults', 'No articles found')}</h3>
                <p className="text-gray-400 mb-6">{t('blog.tryDifferent', 'Widen your search or clear filters to see all available posts.')}</p>
                <Button
                  variant="outline"
                  className="border-neon text-neon hover:bg-neon hover:text-dark"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                    setActiveFilters([]);
                  }}
                >
                  {t('blog.resetFilters', 'Reset Search & Filters')}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Additional space at the bottom */}
          <div className="mb-16"></div>
        </motion.div>
      </main>
      <Footer />
    </div>
  );
}

// BlogPost interface definition updated for multilingual support
interface BlogPost {
  id: number;
  title: {
    [key: string]: string;
  };
  excerpt: {
    [key: string]: string;
  };
  date: string;
  category: {
    [key: string]: string;
  };
  readTime: {
    [key: string]: string;
  };
  slug: string;
  featured: boolean;
  trending: boolean;
  tags: string[];
  // content is not needed for the card display
}

// BlogPostCard component with translation support
function BlogPostCard({ post, variants }: { post: BlogPost, variants: any }) {
    const { t, i18n } = useTranslation();
    const [isHovered, setIsHovered] = useState(false);
    
    // Get current language code (e.g., 'en', 'ar') or fallback to 'en'
    const currentLang = i18n.language.split('-')[0] || 'en';

    return (
        <motion.div variants={variants}>
            <Card
                className="bg-dark/70 backdrop-blur-sm border border-white/10 overflow-hidden h-full flex flex-col transform transition-all duration-300 hover:-translate-y-2 hover:shadow-[0_0_35px_rgba(0,255,247,0.15)] hover:border-white/20" // Enhanced hover effect
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <CardHeader className="pb-4"> {/* Reduced padding */}
                    <div className="flex justify-between items-start mb-3"> {/* items-start */}
                        <div className="flex gap-2 flex-wrap">
                            <Badge variant="outline" className="text-xs border-neon text-neon bg-neon/10 px-3 py-0.5"> {/* Adjusted padding */}
                                {post.category[currentLang] || post.category.en}
                            </Badge>
                            {post.featured && (
                                <Badge className="text-xs bg-purple-500/20 text-purple-300 border-purple-500/30 px-2 py-0.5 flex items-center">
                                    <Bookmark className="h-3 w-3 mr-1" /> {t('blog.featured', 'Featured')}
                                </Badge>
                            )}
                            {post.trending && (
                                <Badge className="text-xs bg-orange-500/20 text-orange-300 border-orange-500/30 px-2 py-0.5 flex items-center">
                                    <TrendingUp className="h-3 w-3 mr-1" /> {t('blog.trending', 'Trending')}
                                </Badge>
                            )}
                        </div>
                        <CardDescription className="text-gray-400 text-xs flex items-center gap-1 whitespace-nowrap pt-0.5"> {/* Align with badge top */}
                            <Clock className="h-3 w-3" /> {post.readTime[currentLang] || post.readTime.en}
                        </CardDescription>
                    </div>
                    <CardTitle className="text-xl font-bold hover:text-neon transition-colors duration-300">
                        <a href={`/blog/${post.slug}`} className="hover:underline">
                            {post.title[currentLang] || post.title.en}
                        </a>
                    </CardTitle>
                    <CardDescription className="flex items-center gap-1 text-gray-400 text-xs pt-1"> {/* Added padding-top */}
                        <Calendar className="h-3 w-3" /> {post.date}
                    </CardDescription>
                </CardHeader>
                <CardContent className="flex-1 pt-0 pb-4"> {/* Reduced padding */}
                    <p className="text-gray-400 text-sm line-clamp-3 mb-2">
                        {post.excerpt[currentLang] || post.excerpt.en}
                    </p>
                    <div className="flex flex-wrap gap-1">
                        {post.tags.slice(0, 3).map(tag => (
                            <Badge
                                key={tag}
                                variant="secondary" // Use secondary variant for tags
                                className="text-xs bg-white/5 border border-transparent text-gray-400 px-2 py-0.5"
                            >
                                {tag}
                            </Badge>
                        ))}
                        {post.tags.length > 3 && (
                            <Badge variant="secondary" className="text-xs bg-white/5 border border-transparent text-gray-400 px-2 py-0.5">
                                +{post.tags.length - 3} more
                            </Badge>
                        )}
                    </div>
                </CardContent>
                <CardFooter className="pt-0"> {/* Reduced padding */}
                    <Button
                        variant="ghost"
                        className="text-neon hover:bg-neon/10 group w-full justify-center text-sm py-2 h-auto" // Adjusted size
                        asChild
                    >
                        <a href={`/blog/${post.slug}`} className="text-neon hover:bg-neon/10 group flex items-center gap-2">
                            {t('blog.readMore', 'Read More')} <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                        </a>
                    </Button>
                </CardFooter>
            </Card>
        </motion.div>
    );
}